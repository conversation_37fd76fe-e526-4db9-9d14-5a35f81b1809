#!/bin/bash

# Telegram Bot Web Admin Panel Setup Script
echo "🚀 Setting up Telegram Bot Web Admin Panel..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_requirements() {
    print_status "Checking requirements..."
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is not installed. Please install Python 3.8 or higher."
        exit 1
    fi
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 16 or higher."
        exit 1
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm."
        exit 1
    fi
    
    print_success "All requirements are met!"
}

# Setup backend
setup_backend() {
    print_status "Setting up backend..."
    
    cd backend
    
    # Create virtual environment
    print_status "Creating Python virtual environment..."
    python3 -m venv venv
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Install dependencies
    print_status "Installing Python dependencies..."
    pip install -r requirements.txt
    
    # Copy environment file
    if [ ! -f .env ]; then
        print_status "Creating environment file..."
        cp .env.example .env
        print_warning "Please edit backend/.env with your configuration!"
    fi
    
    cd ..
    print_success "Backend setup completed!"
}

# Setup frontend
setup_frontend() {
    print_status "Setting up frontend..."
    
    cd frontend
    
    # Install dependencies
    print_status "Installing Node.js dependencies..."
    npm install
    
    # Copy environment file
    if [ ! -f .env ]; then
        print_status "Creating environment file..."
        cp .env.example .env
        print_warning "Please edit frontend/.env with your API URL!"
    fi
    
    cd ..
    print_success "Frontend setup completed!"
}

# Create startup scripts
create_scripts() {
    print_status "Creating startup scripts..."
    
    # Backend start script
    cat > start-backend.sh << 'EOF'
#!/bin/bash
cd backend
source venv/bin/activate
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
EOF
    chmod +x start-backend.sh
    
    # Frontend start script
    cat > start-frontend.sh << 'EOF'
#!/bin/bash
cd frontend
npm start
EOF
    chmod +x start-frontend.sh
    
    # Combined start script
    cat > start-all.sh << 'EOF'
#!/bin/bash
echo "Starting Telegram Bot Admin Panel..."

# Start backend in background
echo "Starting backend..."
./start-backend.sh &
BACKEND_PID=$!

# Wait a bit for backend to start
sleep 5

# Start frontend
echo "Starting frontend..."
./start-frontend.sh &
FRONTEND_PID=$!

echo "Backend PID: $BACKEND_PID"
echo "Frontend PID: $FRONTEND_PID"
echo ""
echo "🎉 Admin panel is starting up!"
echo "📊 Frontend: http://localhost:3000"
echo "🔧 Backend API: http://localhost:8000"
echo "📚 API Docs: http://localhost:8000/docs"
echo ""
echo "Press Ctrl+C to stop all services"

# Wait for user interrupt
trap 'kill $BACKEND_PID $FRONTEND_PID; exit' INT
wait
EOF
    chmod +x start-all.sh
    
    print_success "Startup scripts created!"
}

# Main setup function
main() {
    echo "🎯 Telegram Bot Web Admin Panel Setup"
    echo "======================================"
    echo ""
    
    check_requirements
    setup_backend
    setup_frontend
    create_scripts
    
    echo ""
    echo "🎉 Setup completed successfully!"
    echo ""
    echo "📋 Next steps:"
    echo "1. Configure backend/.env with your database and settings"
    echo "2. Configure frontend/.env with your API URL"
    echo "3. Start the application with: ./start-all.sh"
    echo ""
    echo "📚 Documentation:"
    echo "- README.md - Complete setup and usage guide"
    echo "- API Docs: http://localhost:8000/docs (after starting)"
    echo ""
    echo "🔧 Manual start commands:"
    echo "- Backend only: ./start-backend.sh"
    echo "- Frontend only: ./start-frontend.sh"
    echo "- Both services: ./start-all.sh"
    echo ""
    echo "🐳 Docker deployment:"
    echo "- docker-compose up -d"
    echo ""
    print_success "Ready to launch! 🚀"
}

# Run main function
main
