"""
Real-time statistics and update service for Web Admin Panel
Handles live data aggregation and WebSocket broadcasting
"""

import asyncio
import logging
from typing import Dict, Any, List
from datetime import datetime, timedelta

from app.core.database import database_manager
from app.core.redis_client import redis_client
from app.core.websocket_manager import websocket_manager
from app.core.config import settings

logger = logging.getLogger(__name__)


class RealtimeService:
    """Service for real-time statistics and updates"""
    
    def __init__(self):
        self.db = database_manager
        self.cache = redis_client
        self.ws_manager = websocket_manager
        self.stats_task: asyncio.Task = None
        self.monitoring_task: asyncio.Task = None
    
    async def start(self):
        """Start real-time services"""
        try:
            # Start statistics aggregation task
            self.stats_task = asyncio.create_task(self._stats_aggregation_loop())
            
            # Start system monitoring task
            self.monitoring_task = asyncio.create_task(self._system_monitoring_loop())
            
            logger.info("Real-time service started successfully")
            
        except Exception as e:
            logger.error(f"Error starting real-time service: {e}")
    
    async def stop(self):
        """Stop real-time services"""
        try:
            if self.stats_task:
                self.stats_task.cancel()
            
            if self.monitoring_task:
                self.monitoring_task.cancel()
            
            logger.info("Real-time service stopped")
            
        except Exception as e:
            logger.error(f"Error stopping real-time service: {e}")
    
    async def _stats_aggregation_loop(self):
        """Continuously aggregate and broadcast statistics"""
        while True:
            try:
                # Aggregate current statistics
                stats = await self._aggregate_live_stats()
                
                # Cache the stats
                await self.cache.set("live_stats", stats, ttl=30)
                
                # Broadcast to connected admins
                await self._broadcast_stats_update(stats)
                
                # Wait before next aggregation
                await asyncio.sleep(30)  # Update every 30 seconds
                
            except Exception as e:
                logger.error(f"Error in stats aggregation loop: {e}")
                await asyncio.sleep(10)
    
    async def _system_monitoring_loop(self):
        """Monitor system health and performance"""
        while True:
            try:
                # Check system health
                health_data = await self._check_system_health()
                
                # Broadcast health updates if there are issues
                if health_data.get("has_issues"):
                    await self._broadcast_system_alert(health_data)
                
                # Monitor performance metrics
                performance_data = await self._check_performance_metrics()
                
                # Cache performance data
                await self.cache.set("performance_metrics", performance_data, ttl=60)
                
                # Wait before next check
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"Error in system monitoring loop: {e}")
                await asyncio.sleep(30)
    
    async def _aggregate_live_stats(self) -> Dict[str, Any]:
        """Aggregate live statistics from database"""
        try:
            current_time = datetime.now()
            today_start = int(current_time.replace(hour=0, minute=0, second=0, microsecond=0).timestamp())
            hour_ago = int((current_time - timedelta(hours=1)).timestamp())
            
            # Parallel aggregation for better performance
            tasks = [
                self._get_user_stats(today_start, hour_ago),
                self._get_withdrawal_stats(today_start, hour_ago),
                self._get_activity_stats(hour_ago),
                self._get_system_stats()
            ]
            
            results = await asyncio.gather(*tasks)
            
            return {
                "user_stats": results[0],
                "withdrawal_stats": results[1],
                "activity_stats": results[2],
                "system_stats": results[3],
                "timestamp": int(current_time.timestamp()),
                "last_updated": current_time.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error aggregating live stats: {e}")
            return {}
    
    async def _get_user_stats(self, today_start: int, hour_ago: int) -> Dict[str, Any]:
        """Get real-time user statistics"""
        try:
            pipeline = [
                {
                    "$facet": {
                        "total_count": [{"$count": "count"}],
                        "active_count": [
                            {"$match": {"banned": False}},
                            {"$count": "count"}
                        ],
                        "new_today": [
                            {"$match": {"created_at": {"$gte": today_start}}},
                            {"$count": "count"}
                        ],
                        "new_last_hour": [
                            {"$match": {"created_at": {"$gte": hour_ago}}},
                            {"$count": "count"}
                        ],
                        "total_balance": [
                            {
                                "$group": {
                                    "_id": None,
                                    "total": {"$sum": "$balance"}
                                }
                            }
                        ]
                    }
                }
            ]
            
            result = await self.db.users.aggregate(pipeline).to_list(1)
            if not result:
                return {}
            
            data = result[0]
            return {
                "total_users": data["total_count"][0]["count"] if data["total_count"] else 0,
                "active_users": data["active_count"][0]["count"] if data["active_count"] else 0,
                "new_today": data["new_today"][0]["count"] if data["new_today"] else 0,
                "new_last_hour": data["new_last_hour"][0]["count"] if data["new_last_hour"] else 0,
                "total_balance": data["total_balance"][0]["total"] if data["total_balance"] else 0
            }
            
        except Exception as e:
            logger.error(f"Error getting user stats: {e}")
            return {}
    
    async def _get_withdrawal_stats(self, today_start: int, hour_ago: int) -> Dict[str, Any]:
        """Get real-time withdrawal statistics"""
        try:
            pipeline = [
                {
                    "$facet": {
                        "pending_count": [
                            {"$match": {"status": "Under review"}},
                            {"$count": "count"}
                        ],
                        "pending_amount": [
                            {"$match": {"status": "Under review"}},
                            {
                                "$group": {
                                    "_id": None,
                                    "total": {"$sum": "$amount"}
                                }
                            }
                        ],
                        "today_count": [
                            {"$match": {"created_at": {"$gte": today_start}}},
                            {"$count": "count"}
                        ],
                        "last_hour_count": [
                            {"$match": {"created_at": {"$gte": hour_ago}}},
                            {"$count": "count"}
                        ]
                    }
                }
            ]
            
            result = await self.db.withdrawals.aggregate(pipeline).to_list(1)
            if not result:
                return {}
            
            data = result[0]
            return {
                "pending_count": data["pending_count"][0]["count"] if data["pending_count"] else 0,
                "pending_amount": data["pending_amount"][0]["total"] if data["pending_amount"] else 0,
                "today_count": data["today_count"][0]["count"] if data["today_count"] else 0,
                "last_hour_count": data["last_hour_count"][0]["count"] if data["last_hour_count"] else 0
            }
            
        except Exception as e:
            logger.error(f"Error getting withdrawal stats: {e}")
            return {}
    
    async def _get_activity_stats(self, hour_ago: int) -> Dict[str, Any]:
        """Get recent activity statistics"""
        try:
            # Get recent admin actions
            recent_actions = await self.db.admin_logs.count_documents({
                "timestamp": {"$gte": hour_ago}
            })
            
            # Get active WebSocket connections
            ws_stats = self.ws_manager.get_connection_stats()
            
            return {
                "recent_admin_actions": recent_actions,
                "active_connections": ws_stats.get("total_connections", 0),
                "connected_admins": ws_stats.get("connected_admins", 0)
            }
            
        except Exception as e:
            logger.error(f"Error getting activity stats: {e}")
            return {}
    
    async def _get_system_stats(self) -> Dict[str, Any]:
        """Get system performance statistics"""
        try:
            # Database health
            db_health = await self.db.health_check()
            
            # Redis health
            redis_health = await self.cache.health_check()
            
            return {
                "database_status": "healthy" if db_health else "unhealthy",
                "redis_status": "healthy" if redis_health else "unhealthy",
                "uptime": "running"  # Could be enhanced with actual uptime
            }
            
        except Exception as e:
            logger.error(f"Error getting system stats: {e}")
            return {}
    
    async def _check_system_health(self) -> Dict[str, Any]:
        """Check overall system health"""
        try:
            issues = []
            
            # Check database
            if not await self.db.health_check():
                issues.append("Database connection lost")
            
            # Check Redis
            if not await self.cache.health_check():
                issues.append("Redis connection lost")
            
            # Check pending withdrawals
            pending_count = await self.db.withdrawals.count_documents({"status": "Under review"})
            if pending_count > 50:  # Configurable threshold
                issues.append(f"High number of pending withdrawals: {pending_count}")
            
            return {
                "has_issues": len(issues) > 0,
                "issues": issues,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error checking system health: {e}")
            return {"has_issues": True, "issues": ["Health check failed"]}
    
    async def _check_performance_metrics(self) -> Dict[str, Any]:
        """Check system performance metrics"""
        try:
            # This could be enhanced with actual performance monitoring
            return {
                "response_time": "normal",
                "memory_usage": "normal",
                "cpu_usage": "normal",
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error checking performance metrics: {e}")
            return {}
    
    async def _broadcast_stats_update(self, stats: Dict[str, Any]):
        """Broadcast statistics update to all connected admins"""
        try:
            message = {
                "type": "stats_update",
                "data": stats,
                "timestamp": datetime.now().isoformat()
            }
            
            await self.ws_manager.broadcast_to_all(message)
            
            # Also publish to Redis for other instances
            await self.cache.publish("stats_updates", message)
            
        except Exception as e:
            logger.error(f"Error broadcasting stats update: {e}")
    
    async def _broadcast_system_alert(self, health_data: Dict[str, Any]):
        """Broadcast system alert to all connected admins"""
        try:
            message = {
                "type": "system_alert",
                "data": health_data,
                "timestamp": datetime.now().isoformat()
            }
            
            await self.ws_manager.broadcast_to_all(message)
            
            # Also publish to Redis
            await self.cache.publish("system_updates", message)
            
        except Exception as e:
            logger.error(f"Error broadcasting system alert: {e}")
    
    async def broadcast_user_update(self, user_id: int, action: str, data: Dict[str, Any]):
        """Broadcast user-related update"""
        try:
            message = {
                "type": "user_update",
                "data": {
                    "user_id": user_id,
                    "action": action,
                    "details": data
                },
                "timestamp": datetime.now().isoformat()
            }
            
            await self.ws_manager.broadcast_to_all(message)
            await self.cache.publish("user_updates", message)
            
        except Exception as e:
            logger.error(f"Error broadcasting user update: {e}")
    
    async def broadcast_withdrawal_update(self, withdrawal_id: str, action: str, data: Dict[str, Any]):
        """Broadcast withdrawal-related update"""
        try:
            message = {
                "type": "withdrawal_update",
                "data": {
                    "withdrawal_id": withdrawal_id,
                    "action": action,
                    "details": data
                },
                "timestamp": datetime.now().isoformat()
            }
            
            await self.ws_manager.broadcast_to_all(message)
            await self.cache.publish("withdrawal_updates", message)
            
        except Exception as e:
            logger.error(f"Error broadcasting withdrawal update: {e}")


# Global real-time service instance
realtime_service = RealtimeService()
