"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("./core");
const validation_1 = require("./validation");
const applicator_1 = require("./applicator");
const dynamic_1 = require("./dynamic");
const next_1 = require("./next");
const unevaluated_1 = require("./unevaluated");
const format_1 = require("./format");
const metadata_1 = require("./metadata");
const draft2020Vocabularies = [
    dynamic_1.default,
    core_1.default,
    validation_1.default,
    (0, applicator_1.default)(true),
    format_1.default,
    metadata_1.metadataVocabulary,
    metadata_1.contentVocabulary,
    next_1.default,
    unevaluated_1.default,
];
exports.default = draft2020Vocabularies;
//# sourceMappingURL=draft2020.js.map