"""
Redis client for caching and real-time features
"""

import json
import logging
from typing import Optional, Any, Dict, List

try:
    import redis.asyncio as redis
    from redis.asyncio import Redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    Redis = None

from app.core.config import settings

logger = logging.getLogger(__name__)


class RedisClient:
    """Redis client for caching and pub/sub operations"""
    
    def __init__(self):
        self.redis: Optional[Redis] = None
        self._connected: bool = False
    
    async def connect(self) -> bool:
        """Connect to Redis"""
        if not REDIS_AVAILABLE:
            logger.warning("Redis module not available, running without Redis")
            return False

        if not settings.REDIS_URL:
            logger.warning("Redis URL not configured, running without Redis")
            return False

        try:
            logger.info("Connecting to Redis...")

            # Parse Redis URL
            self.redis = redis.from_url(
                settings.REDIS_URL,
                password=settings.REDIS_PASSWORD,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )
            
            # Test connection
            await self.redis.ping()
            
            self._connected = True
            logger.info("Successfully connected to Redis")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            return False
    
    async def disconnect(self):
        """Disconnect from Redis"""
        if self.redis:
            await self.redis.close()
            self._connected = False
            logger.info("Disconnected from Redis")
    
    async def health_check(self) -> bool:
        """Check Redis health"""
        try:
            if not self.redis:
                return False
            await self.redis.ping()
            return True
        except Exception:
            return False
    
    @property
    def is_connected(self) -> bool:
        """Check if Redis is connected"""
        return self._connected
    
    # Cache operations
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        try:
            if not self.redis:
                return None
            
            value = await self.redis.get(key)
            if value:
                return json.loads(value)
            return None
        except Exception as e:
            logger.error(f"Error getting cache key {key}: {e}")
            return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache"""
        try:
            if not self.redis:
                return False
            
            serialized_value = json.dumps(value, default=str)
            
            if ttl:
                await self.redis.setex(key, ttl, serialized_value)
            else:
                await self.redis.set(key, serialized_value)
            
            return True
        except Exception as e:
            logger.error(f"Error setting cache key {key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete key from cache"""
        try:
            if not self.redis:
                return False
            
            await self.redis.delete(key)
            return True
        except Exception as e:
            logger.error(f"Error deleting cache key {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache"""
        try:
            if not self.redis:
                return False
            
            return bool(await self.redis.exists(key))
        except Exception as e:
            logger.error(f"Error checking cache key {key}: {e}")
            return False
    
    async def increment(self, key: str, amount: int = 1) -> Optional[int]:
        """Increment counter"""
        try:
            if not self.redis:
                return None
            
            return await self.redis.incrby(key, amount)
        except Exception as e:
            logger.error(f"Error incrementing key {key}: {e}")
            return None
    
    async def expire(self, key: str, ttl: int) -> bool:
        """Set expiration for key"""
        try:
            if not self.redis:
                return False
            
            await self.redis.expire(key, ttl)
            return True
        except Exception as e:
            logger.error(f"Error setting expiration for key {key}: {e}")
            return False
    
    # Pub/Sub operations for real-time updates
    async def publish(self, channel: str, message: Dict[str, Any]) -> bool:
        """Publish message to channel"""
        try:
            if not self.redis:
                return False
            
            serialized_message = json.dumps(message, default=str)
            await self.redis.publish(channel, serialized_message)
            return True
        except Exception as e:
            logger.error(f"Error publishing to channel {channel}: {e}")
            return False
    
    async def subscribe(self, channel: str):
        """Subscribe to channel"""
        try:
            if not self.redis:
                return None
            
            pubsub = self.redis.pubsub()
            await pubsub.subscribe(channel)
            return pubsub
        except Exception as e:
            logger.error(f"Error subscribing to channel {channel}: {e}")
            return None
    
    # Hash operations for complex data
    async def hget(self, name: str, key: str) -> Optional[Any]:
        """Get hash field value"""
        try:
            if not self.redis:
                return None
            
            value = await self.redis.hget(name, key)
            if value:
                return json.loads(value)
            return None
        except Exception as e:
            logger.error(f"Error getting hash field {name}:{key}: {e}")
            return None
    
    async def hset(self, name: str, key: str, value: Any) -> bool:
        """Set hash field value"""
        try:
            if not self.redis:
                return False
            
            serialized_value = json.dumps(value, default=str)
            await self.redis.hset(name, key, serialized_value)
            return True
        except Exception as e:
            logger.error(f"Error setting hash field {name}:{key}: {e}")
            return False
    
    async def hgetall(self, name: str) -> Dict[str, Any]:
        """Get all hash fields"""
        try:
            if not self.redis:
                return {}
            
            hash_data = await self.redis.hgetall(name)
            result = {}
            for key, value in hash_data.items():
                try:
                    result[key] = json.loads(value)
                except json.JSONDecodeError:
                    result[key] = value
            return result
        except Exception as e:
            logger.error(f"Error getting hash {name}: {e}")
            return {}
    
    # List operations for queues
    async def lpush(self, key: str, *values: Any) -> Optional[int]:
        """Push values to left of list"""
        try:
            if not self.redis:
                return None
            
            serialized_values = [json.dumps(value, default=str) for value in values]
            return await self.redis.lpush(key, *serialized_values)
        except Exception as e:
            logger.error(f"Error pushing to list {key}: {e}")
            return None
    
    async def rpop(self, key: str) -> Optional[Any]:
        """Pop value from right of list"""
        try:
            if not self.redis:
                return None
            
            value = await self.redis.rpop(key)
            if value:
                return json.loads(value)
            return None
        except Exception as e:
            logger.error(f"Error popping from list {key}: {e}")
            return None


# Global Redis client instance
redis_client = RedisClient()
