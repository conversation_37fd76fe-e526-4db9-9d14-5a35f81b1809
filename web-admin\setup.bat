@echo off
echo 🚀 Setting up Telegram Bot Web Admin Panel...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed. Please install Python 3.8 or higher.
    pause
    exit /b 1
)

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js is not installed. Please install Node.js 16 or higher.
    pause
    exit /b 1
)

echo ✅ Requirements check passed!
echo.

REM Setup Backend
echo 📦 Setting up backend...
cd backend

REM Create virtual environment
echo Creating Python virtual environment...
python -m venv venv

REM Activate virtual environment and install dependencies
echo Installing Python dependencies...
call venv\Scripts\activate.bat
pip install -r requirements.txt

REM Copy environment file if it doesn't exist
if not exist .env (
    echo Creating environment file...
    copy .env.example .env
    echo ⚠️  Please edit backend\.env with your configuration!
)

cd ..
echo ✅ Backend setup completed!
echo.

REM Setup Frontend
echo 🎨 Setting up frontend...
cd frontend

REM Install dependencies
echo Installing Node.js dependencies...
npm install

REM Copy environment file if it doesn't exist
if not exist .env (
    echo Creating environment file...
    copy .env.example .env
    echo ⚠️  Please edit frontend\.env with your API URL!
)

cd ..
echo ✅ Frontend setup completed!
echo.

REM Create startup scripts
echo 📝 Creating startup scripts...

REM Backend start script
echo @echo off > start-backend.bat
echo cd backend >> start-backend.bat
echo call venv\Scripts\activate.bat >> start-backend.bat
echo uvicorn app.main:app --reload --host 0.0.0.0 --port 8000 >> start-backend.bat

REM Frontend start script
echo @echo off > start-frontend.bat
echo cd frontend >> start-frontend.bat
echo npm start >> start-frontend.bat

REM Combined start script
echo @echo off > start-all.bat
echo echo Starting Telegram Bot Admin Panel... >> start-all.bat
echo echo. >> start-all.bat
echo echo Starting backend... >> start-all.bat
echo start "Backend" cmd /k start-backend.bat >> start-all.bat
echo timeout /t 5 /nobreak ^>nul >> start-all.bat
echo echo Starting frontend... >> start-all.bat
echo start "Frontend" cmd /k start-frontend.bat >> start-all.bat
echo echo. >> start-all.bat
echo echo 🎉 Admin panel is starting up! >> start-all.bat
echo echo 📊 Frontend: http://localhost:3000 >> start-all.bat
echo echo 🔧 Backend API: http://localhost:8000 >> start-all.bat
echo echo 📚 API Docs: http://localhost:8000/docs >> start-all.bat

echo ✅ Startup scripts created!
echo.

echo 🎉 Setup completed successfully!
echo.
echo 📋 Next steps:
echo 1. Configure backend\.env with your database and settings
echo 2. Configure frontend\.env with your API URL
echo 3. Start the application with: start-all.bat
echo.
echo 📚 Documentation:
echo - README.md - Complete setup and usage guide
echo - API Docs: http://localhost:8000/docs (after starting)
echo.
echo 🔧 Manual start commands:
echo - Backend only: start-backend.bat
echo - Frontend only: start-frontend.bat
echo - Both services: start-all.bat
echo.
echo 🐳 Docker deployment:
echo - docker-compose up -d
echo.
echo ✅ Ready to launch! 🚀
echo.
pause
