import React from 'react';
import { Box, Typography, Card, CardContent } from '@mui/material';

function SettingsPage() {
  return (
    <Box>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 600, mb: 1 }}>
          Settings
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Configure system settings and preferences
        </Typography>
      </Box>

      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Settings Features
          </Typography>
          <Typography variant="body2" color="text.secondary">
            This page will include:
            <br />• Profile settings
            <br />• Password change
            <br />• Notification preferences
            <br />• Theme settings
            <br />• API configuration
            <br />• Security settings
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
}

export default SettingsPage;
