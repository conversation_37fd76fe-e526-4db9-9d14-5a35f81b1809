#!/bin/bash

# Production Deployment Script for Telegram Bot Admin Panel
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_FILE="docker-compose.prod.yml"
ENV_FILE=".env.prod"
BACKUP_DIR="./backups"
LOG_FILE="./logs/deploy.log"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

# Create log directory
mkdir -p "$(dirname "$LOG_FILE")"

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed"
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed"
        exit 1
    fi
    
    # Check environment file
    if [ ! -f "$ENV_FILE" ]; then
        print_error "Environment file $ENV_FILE not found"
        print_status "Please create $ENV_FILE with required variables"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Function to backup current deployment
backup_current() {
    print_status "Creating backup of current deployment..."
    
    mkdir -p "$BACKUP_DIR"
    BACKUP_NAME="backup_$(date +%Y%m%d_%H%M%S)"
    BACKUP_PATH="$BACKUP_DIR/$BACKUP_NAME"
    
    # Create backup directory
    mkdir -p "$BACKUP_PATH"
    
    # Backup database (if MongoDB is accessible)
    if [ -n "$MONGODB_URI" ]; then
        print_status "Backing up database..."
        # Add database backup logic here
        # mongodump --uri="$MONGODB_URI" --out="$BACKUP_PATH/db"
    fi
    
    # Backup configuration files
    cp -r ./nginx "$BACKUP_PATH/" 2>/dev/null || true
    cp "$ENV_FILE" "$BACKUP_PATH/" 2>/dev/null || true
    cp "$COMPOSE_FILE" "$BACKUP_PATH/" 2>/dev/null || true
    
    print_success "Backup created at $BACKUP_PATH"
}

# Function to build images
build_images() {
    print_status "Building Docker images..."
    
    # Load environment variables
    export $(cat "$ENV_FILE" | grep -v '^#' | xargs)
    
    # Build images
    docker-compose -f "$COMPOSE_FILE" build --no-cache
    
    print_success "Docker images built successfully"
}

# Function to deploy services
deploy_services() {
    print_status "Deploying services..."
    
    # Load environment variables
    export $(cat "$ENV_FILE" | grep -v '^#' | xargs)
    
    # Stop existing services
    print_status "Stopping existing services..."
    docker-compose -f "$COMPOSE_FILE" down --remove-orphans
    
    # Start new services
    print_status "Starting new services..."
    docker-compose -f "$COMPOSE_FILE" up -d
    
    print_success "Services deployed successfully"
}

# Function to wait for services
wait_for_services() {
    print_status "Waiting for services to be ready..."
    
    # Wait for backend
    print_status "Waiting for backend service..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if docker-compose -f "$COMPOSE_FILE" exec -T backend curl -f http://localhost:8000/health &>/dev/null; then
            print_success "Backend service is ready"
            break
        fi
        sleep 2
        timeout=$((timeout - 2))
    done
    
    if [ $timeout -le 0 ]; then
        print_error "Backend service failed to start"
        return 1
    fi
    
    # Wait for frontend
    print_status "Waiting for frontend service..."
    timeout=30
    while [ $timeout -gt 0 ]; do
        if docker-compose -f "$COMPOSE_FILE" exec -T nginx curl -f http://localhost/health &>/dev/null; then
            print_success "Frontend service is ready"
            break
        fi
        sleep 2
        timeout=$((timeout - 2))
    done
    
    if [ $timeout -le 0 ]; then
        print_error "Frontend service failed to start"
        return 1
    fi
    
    print_success "All services are ready"
}

# Function to run health checks
health_check() {
    print_status "Running health checks..."
    
    # Check service status
    if ! docker-compose -f "$COMPOSE_FILE" ps | grep -q "Up"; then
        print_error "Some services are not running"
        return 1
    fi
    
    # Check API health
    if ! curl -f http://localhost/api/v1/health &>/dev/null; then
        print_error "API health check failed"
        return 1
    fi
    
    # Check frontend
    if ! curl -f http://localhost/health &>/dev/null; then
        print_error "Frontend health check failed"
        return 1
    fi
    
    print_success "Health checks passed"
}

# Function to cleanup old images
cleanup() {
    print_status "Cleaning up old Docker images..."
    
    # Remove dangling images
    docker image prune -f
    
    # Remove old backups (keep last 10)
    if [ -d "$BACKUP_DIR" ]; then
        cd "$BACKUP_DIR"
        ls -t | tail -n +11 | xargs -r rm -rf
        cd - > /dev/null
    fi
    
    print_success "Cleanup completed"
}

# Function to show deployment status
show_status() {
    print_status "Deployment Status:"
    echo "===================="
    
    # Show running services
    docker-compose -f "$COMPOSE_FILE" ps
    
    echo ""
    print_status "Service URLs:"
    echo "Frontend: http://localhost"
    echo "Backend API: http://localhost/api/v1"
    echo "API Documentation: http://localhost/api/v1/docs"
    
    if docker-compose -f "$COMPOSE_FILE" ps | grep -q "grafana"; then
        echo "Grafana: http://localhost:3001"
    fi
    
    if docker-compose -f "$COMPOSE_FILE" ps | grep -q "prometheus"; then
        echo "Prometheus: http://localhost:9090"
    fi
}

# Function to rollback deployment
rollback() {
    print_warning "Rolling back deployment..."
    
    # Find latest backup
    LATEST_BACKUP=$(ls -t "$BACKUP_DIR" | head -n 1)
    
    if [ -z "$LATEST_BACKUP" ]; then
        print_error "No backup found for rollback"
        exit 1
    fi
    
    print_status "Rolling back to $LATEST_BACKUP"
    
    # Stop current services
    docker-compose -f "$COMPOSE_FILE" down
    
    # Restore configuration
    cp "$BACKUP_DIR/$LATEST_BACKUP/$ENV_FILE" ./ 2>/dev/null || true
    
    # Start services with old configuration
    export $(cat "$ENV_FILE" | grep -v '^#' | xargs)
    docker-compose -f "$COMPOSE_FILE" up -d
    
    print_success "Rollback completed"
}

# Main deployment function
main() {
    print_status "Starting production deployment..."
    echo "$(date): Starting deployment" >> "$LOG_FILE"
    
    case "${1:-deploy}" in
        "deploy")
            check_prerequisites
            backup_current
            build_images
            deploy_services
            wait_for_services
            health_check
            cleanup
            show_status
            print_success "Deployment completed successfully!"
            ;;
        "rollback")
            rollback
            ;;
        "status")
            show_status
            ;;
        "health")
            health_check
            ;;
        "logs")
            docker-compose -f "$COMPOSE_FILE" logs -f "${2:-}"
            ;;
        "stop")
            print_status "Stopping services..."
            docker-compose -f "$COMPOSE_FILE" down
            print_success "Services stopped"
            ;;
        "restart")
            print_status "Restarting services..."
            docker-compose -f "$COMPOSE_FILE" restart "${2:-}"
            print_success "Services restarted"
            ;;
        *)
            echo "Usage: $0 {deploy|rollback|status|health|logs|stop|restart}"
            echo ""
            echo "Commands:"
            echo "  deploy   - Full deployment (default)"
            echo "  rollback - Rollback to previous version"
            echo "  status   - Show deployment status"
            echo "  health   - Run health checks"
            echo "  logs     - Show service logs"
            echo "  stop     - Stop all services"
            echo "  restart  - Restart services"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
