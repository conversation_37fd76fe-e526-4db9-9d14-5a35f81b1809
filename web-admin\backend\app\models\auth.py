"""
Authentication models for Web Admin Panel
"""

from typing import Dict, Optional
from pydantic import BaseModel, Field


class LoginRequest(BaseModel):
    """Login request model"""
    admin_id: int
    password: str


class LoginResponse(BaseModel):
    """Login response model"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    admin_info: Dict[str, Any]


class RefreshTokenRequest(BaseModel):
    """Refresh token request model"""
    refresh_token: str


class TokenResponse(BaseModel):
    """Token response model"""
    access_token: str
    token_type: str = "bearer"
    expires_in: int


class AdminInfo(BaseModel):
    """Admin information model"""
    admin_id: int
    role: str
    permissions: Dict[str, bool]
    last_login: Optional[int] = None
    is_active: bool = True
    created_at: int
    updated_at: int


class ChangePasswordRequest(BaseModel):
    """Change password request model"""
    current_password: str
    new_password: str


class AdminPermissions(BaseModel):
    """Admin permissions model"""
    view_dashboard: bool = True
    manage_users: bool = False
    manage_withdrawals: bool = False
    manage_referrals: bool = False
    manage_admins: bool = False
    manage_bot_settings: bool = False
    view_analytics: bool = False
    manage_broadcasts: bool = False
    manage_gift_codes: bool = False
    system_maintenance: bool = False


class AdminCreate(BaseModel):
    """Admin creation model"""
    admin_id: int
    role: str = "admin"
    permissions: AdminPermissions = Field(default_factory=AdminPermissions)
    password: Optional[str] = None


class AdminUpdate(BaseModel):
    """Admin update model"""
    role: Optional[str] = None
    permissions: Optional[AdminPermissions] = None
    is_active: Optional[bool] = None
