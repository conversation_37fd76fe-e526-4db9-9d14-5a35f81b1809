"""
Dashboard endpoints for Web Admin Panel
"""

import logging
from typing import Dict, Any, List
from fastapi import APIRouter, HTTPException, status, Depends

from app.core.auth import get_current_admin, require_permission
from app.services.user_service import user_service
from app.services.withdrawal_service import withdrawal_service
from app.services.realtime_service import realtime_service
from app.core.redis_client import redis_client

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/stats")
async def get_dashboard_stats(
    current_admin: dict = Depends(require_permission("view_dashboard"))
):
    """Get comprehensive dashboard statistics"""
    try:
        # Get user statistics
        user_stats = await user_service.get_user_stats()
        
        # Get withdrawal statistics
        withdrawal_stats = await withdrawal_service.get_withdrawal_stats()
        
        # Get withdrawal method statistics
        withdrawal_method_stats = await withdrawal_service.get_withdrawal_method_stats()
        
        # Get top users by balance
        top_users_balance = await user_service.get_top_users_by_balance(limit=5)
        
        # Get top users by referrals
        top_users_referrals = await user_service.get_top_users_by_referrals(limit=5)
        
        return {
            "user_stats": user_stats.dict(),
            "withdrawal_stats": withdrawal_stats.dict(),
            "withdrawal_method_stats": [stat.dict() for stat in withdrawal_method_stats],
            "top_users": {
                "by_balance": [user.dict() for user in top_users_balance],
                "by_referrals": top_users_referrals
            },
            "timestamp": user_service.db.get_current_timestamp()
        }
        
    except Exception as e:
        logger.error(f"Error getting dashboard stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving dashboard statistics"
        )


@router.get("/recent-activity")
async def get_recent_activity(
    limit: int = 20,
    current_admin: dict = Depends(require_permission("view_dashboard"))
):
    """Get recent activity across the system"""
    try:
        # Get recent users (last 24 hours)
        recent_users = await user_service.search_users(
            user_service.UserSearchRequest(
                page=1,
                page_size=limit,
                sort_by="created_at",
                sort_order="desc"
            )
        )
        
        # Get recent withdrawals
        recent_withdrawals = await withdrawal_service.search_withdrawals(
            withdrawal_service.WithdrawalSearchRequest(
                page=1,
                page_size=limit,
                sort_by="created_at",
                sort_order="desc"
            )
        )
        
        return {
            "recent_users": [user.dict() for user in recent_users.users],
            "recent_withdrawals": [withdrawal.dict() for withdrawal in recent_withdrawals.withdrawals],
            "timestamp": user_service.db.get_current_timestamp()
        }
        
    except Exception as e:
        logger.error(f"Error getting recent activity: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving recent activity"
        )


@router.get("/system-health")
async def get_system_health(
    current_admin: dict = Depends(require_permission("view_dashboard"))
):
    """Get system health information"""
    try:
        # Check database health
        db_health = await user_service.db.health_check()
        
        # Check Redis health
        redis_health = await redis_client.health_check()
        
        # Get connection stats if available
        connection_stats = {}
        try:
            from app.core.websocket_manager import websocket_manager
            connection_stats = websocket_manager.get_connection_stats()
        except Exception:
            pass
        
        return {
            "database": {
                "status": "healthy" if db_health else "unhealthy",
                "connected": db_health
            },
            "redis": {
                "status": "healthy" if redis_health else "unhealthy",
                "connected": redis_health
            },
            "websocket": connection_stats,
            "timestamp": user_service.db.get_current_timestamp()
        }
        
    except Exception as e:
        logger.error(f"Error getting system health: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving system health"
        )


@router.get("/analytics/trends")
async def get_analytics_trends(
    period: str = "7d",  # 1d, 7d, 30d
    current_admin: dict = Depends(require_permission("view_analytics"))
):
    """Get analytics trends for specified period"""
    try:
        # This would typically involve more complex aggregation
        # For now, return basic trend data
        
        from datetime import datetime, timedelta
        
        # Calculate date range based on period
        now = datetime.now()
        if period == "1d":
            start_date = now - timedelta(days=1)
            interval = "hour"
        elif period == "7d":
            start_date = now - timedelta(days=7)
            interval = "day"
        elif period == "30d":
            start_date = now - timedelta(days=30)
            interval = "day"
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid period. Use 1d, 7d, or 30d"
            )
        
        start_timestamp = int(start_date.timestamp())
        
        # Get user registration trends
        user_pipeline = [
            {"$match": {"created_at": {"$gte": start_timestamp}}},
            {
                "$group": {
                    "_id": {
                        "$dateToString": {
                            "format": "%Y-%m-%d" if interval == "day" else "%Y-%m-%d %H:00:00",
                            "date": {"$toDate": {"$multiply": ["$created_at", 1000]}}
                        }
                    },
                    "count": {"$sum": 1}
                }
            },
            {"$sort": {"_id": 1}}
        ]
        
        user_trends = await user_service.db.users.aggregate(user_pipeline).to_list(None)
        
        # Get withdrawal trends
        withdrawal_pipeline = [
            {"$match": {"created_at": {"$gte": start_timestamp}}},
            {
                "$group": {
                    "_id": {
                        "$dateToString": {
                            "format": "%Y-%m-%d" if interval == "day" else "%Y-%m-%d %H:00:00",
                            "date": {"$toDate": {"$multiply": ["$created_at", 1000]}}
                        }
                    },
                    "count": {"$sum": 1},
                    "amount": {"$sum": "$amount"}
                }
            },
            {"$sort": {"_id": 1}}
        ]
        
        withdrawal_trends = await withdrawal_service.db.withdrawals.aggregate(withdrawal_pipeline).to_list(None)
        
        return {
            "period": period,
            "interval": interval,
            "user_registrations": user_trends,
            "withdrawals": withdrawal_trends,
            "timestamp": user_service.db.get_current_timestamp()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting analytics trends: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving analytics trends"
        )


@router.get("/quick-actions")
async def get_quick_actions(
    current_admin: dict = Depends(get_current_admin)
):
    """Get quick action items for admin dashboard"""
    try:
        admin_permissions = current_admin.get("permissions", {})
        quick_actions = []
        
        # Pending withdrawals
        if admin_permissions.get("manage_withdrawals", False):
            pending_withdrawals = await withdrawal_service.search_withdrawals(
                withdrawal_service.WithdrawalSearchRequest(
                    status="Under review",
                    page=1,
                    page_size=1
                )
            )
            
            quick_actions.append({
                "type": "pending_withdrawals",
                "title": "Pending Withdrawals",
                "count": pending_withdrawals.total,
                "priority": "high" if pending_withdrawals.total > 10 else "medium",
                "action_url": "/withdrawals?status=Under review"
            })
        
        # New users today
        if admin_permissions.get("manage_users", False):
            from datetime import datetime
            today_start = int(datetime.now().replace(hour=0, minute=0, second=0, microsecond=0).timestamp())
            
            new_users_today = await user_service.db.users.count_documents({
                "created_at": {"$gte": today_start}
            })
            
            quick_actions.append({
                "type": "new_users",
                "title": "New Users Today",
                "count": new_users_today,
                "priority": "low",
                "action_url": "/users?sort_by=created_at&sort_order=desc"
            })
        
        # System health alerts
        db_health = await user_service.db.health_check()
        redis_health = await redis_client.health_check()
        
        if not db_health or not redis_health:
            quick_actions.append({
                "type": "system_alert",
                "title": "System Health Alert",
                "count": 1,
                "priority": "critical",
                "action_url": "/dashboard/system-health"
            })
        
        return {
            "quick_actions": quick_actions,
            "timestamp": user_service.db.get_current_timestamp()
        }
        
    except Exception as e:
        logger.error(f"Error getting quick actions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving quick actions"
        )


@router.get("/live-stats")
async def get_live_stats(
    current_admin: dict = Depends(require_permission("view_dashboard"))
):
    """Get real-time live statistics"""
    try:
        # Try to get cached live stats first
        cached_stats = await redis_client.get("live_stats")
        if cached_stats:
            return cached_stats

        # If no cached stats, get current stats
        user_stats = await user_service.get_user_stats()
        withdrawal_stats = await withdrawal_service.get_withdrawal_stats()

        live_stats = {
            "user_stats": user_stats.dict(),
            "withdrawal_stats": withdrawal_stats.dict(),
            "timestamp": user_service.db.get_current_timestamp(),
            "source": "fallback"
        }

        return live_stats

    except Exception as e:
        logger.error(f"Error getting live stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving live statistics"
        )


@router.get("/performance-metrics")
async def get_performance_metrics(
    current_admin: dict = Depends(require_permission("view_dashboard"))
):
    """Get system performance metrics"""
    try:
        # Get cached performance metrics
        metrics = await redis_client.get("performance_metrics")
        if not metrics:
            metrics = {
                "response_time": "normal",
                "memory_usage": "normal",
                "cpu_usage": "normal",
                "timestamp": user_service.db.get_current_timestamp()
            }

        return metrics

    except Exception as e:
        logger.error(f"Error getting performance metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving performance metrics"
        )
