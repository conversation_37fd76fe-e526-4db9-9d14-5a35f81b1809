import React, { useState, useEffect } from 'react';
import {
  Box,
  <PERSON>pography,
  Card,
  CardContent,
  Grid,
  Tabs,
  Tab,
  Switch,
  FormControlLabel,
  Button,
  Alert,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
} from '@mui/material';
import {
  Settings as SettingsIcon,
  People as PeopleIcon,
  Broadcast as BroadcastIcon,
  Analytics as AnalyticsIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`admin-tabpanel-${index}`}
      aria-labelledby={`admin-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
    </div>
  );
}

function AdminPage() {
  const { hasPermission } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  const [botSettings, setBotSettings] = useState<any>({});
  const [admins, setAdmins] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [createAdminOpen, setCreateAdminOpen] = useState(false);
  const [newAdmin, setNewAdmin] = useState({ admin_id: '', role: 'admin' });

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const fetchBotSettings = async () => {
    try {
      const response = await fetch('/api/v1/admin/bot-settings', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setBotSettings(data);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to load bot settings');
    }
  };

  const fetchAdmins = async () => {
    try {
      const response = await fetch('/api/v1/admin/list', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setAdmins(data.admins || []);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to load admins');
    }
  };

  const updateBotSetting = async (setting: string, value: boolean) => {
    try {
      const response = await fetch('/api/v1/admin/bot-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
        body: JSON.stringify({ [setting]: value }),
      });

      if (response.ok) {
        setBotSettings(prev => ({ ...prev, [setting]: value }));
      } else {
        throw new Error('Failed to update setting');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to update setting');
    }
  };

  const createAdmin = async () => {
    try {
      const response = await fetch('/api/v1/admin/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
        body: JSON.stringify({
          admin_id: parseInt(newAdmin.admin_id),
          role: newAdmin.role,
          permissions: {
            view_dashboard: true,
            manage_users: newAdmin.role === 'admin',
            manage_withdrawals: newAdmin.role === 'admin',
            manage_referrals: newAdmin.role === 'admin',
            manage_admins: false,
            manage_bot_settings: false,
            view_analytics: true,
            manage_broadcasts: newAdmin.role === 'admin',
            manage_gift_codes: newAdmin.role === 'admin',
            system_maintenance: false,
          },
        }),
      });

      if (response.ok) {
        setCreateAdminOpen(false);
        setNewAdmin({ admin_id: '', role: 'admin' });
        fetchAdmins();
      } else {
        throw new Error('Failed to create admin');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to create admin');
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([fetchBotSettings(), fetchAdmins()]);
      setLoading(false);
    };

    loadData();
  }, []);

  if (!hasPermission('manage_admins') && !hasPermission('manage_bot_settings')) {
    return (
      <Box sx={{ textAlign: 'center', py: 8 }}>
        <Typography variant="h5" color="text.secondary">
          Access Denied
        </Typography>
        <Typography variant="body1" color="text.secondary">
          You don't have permission to access admin management.
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* Page Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 600, mb: 1 }}>
          Admin Management
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage admin users and system settings
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <Card>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs value={tabValue} onChange={handleTabChange} aria-label="admin tabs">
              {hasPermission('manage_bot_settings') && (
                <Tab
                  label="Bot Settings"
                  icon={<SettingsIcon />}
                  iconPosition="start"
                  id="admin-tab-0"
                  aria-controls="admin-tabpanel-0"
                />
              )}
              {hasPermission('manage_admins') && (
                <Tab
                  label="Admin Users"
                  icon={<PeopleIcon />}
                  iconPosition="start"
                  id="admin-tab-1"
                  aria-controls="admin-tabpanel-1"
                />
              )}
              <Tab
                label="System Logs"
                icon={<AnalyticsIcon />}
                iconPosition="start"
                id="admin-tab-2"
                aria-controls="admin-tabpanel-2"
              />
            </Tabs>
          </Box>

          {/* Bot Settings Tab */}
          {hasPermission('manage_bot_settings') && (
            <TabPanel value={tabValue} index={0}>
              <Box sx={{ p: 3 }}>
                <Typography variant="h6" sx={{ mb: 3 }}>
                  Bot Configuration
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="subtitle1" sx={{ mb: 2 }}>
                          System Controls
                        </Typography>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={botSettings.maintenance_mode || false}
                                onChange={(e) => updateBotSetting('maintenance_mode', e.target.checked)}
                              />
                            }
                            label="Maintenance Mode"
                          />
                          <FormControlLabel
                            control={
                              <Switch
                                checked={botSettings.referral_system_enabled !== false}
                                onChange={(e) => updateBotSetting('referral_system_enabled', e.target.checked)}
                              />
                            }
                            label="Referral System"
                          />
                          <FormControlLabel
                            control={
                              <Switch
                                checked={botSettings.level_rewards_enabled !== false}
                                onChange={(e) => updateBotSetting('level_rewards_enabled', e.target.checked)}
                              />
                            }
                            label="Level Rewards"
                          />
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="subtitle1" sx={{ mb: 2 }}>
                          Withdrawal Settings
                        </Typography>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={botSettings.bank_withdrawals_enabled !== false}
                                onChange={(e) => updateBotSetting('bank_withdrawals_enabled', e.target.checked)}
                              />
                            }
                            label="Bank Withdrawals"
                          />
                          <FormControlLabel
                            control={
                              <Switch
                                checked={botSettings.usdt_withdrawals_enabled !== false}
                                onChange={(e) => updateBotSetting('usdt_withdrawals_enabled', e.target.checked)}
                              />
                            }
                            label="USDT Withdrawals"
                          />
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </Box>
            </TabPanel>
          )}

          {/* Admin Users Tab */}
          {hasPermission('manage_admins') && (
            <TabPanel value={tabValue} index={hasPermission('manage_bot_settings') ? 1 : 0}>
              <Box sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                  <Typography variant="h6">
                    Admin Users
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => setCreateAdminOpen(true)}
                  >
                    Add Admin
                  </Button>
                </Box>

                <TableContainer component={Paper} variant="outlined">
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Admin ID</TableCell>
                        <TableCell>Role</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Last Login</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {admins.map((admin) => (
                        <TableRow key={admin.admin_id}>
                          <TableCell>{admin.admin_id}</TableCell>
                          <TableCell>
                            <Chip
                              label={admin.role}
                              color={admin.role === 'super_admin' ? 'primary' : 'default'}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={admin.is_active ? 'Active' : 'Inactive'}
                              color={admin.is_active ? 'success' : 'error'}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            {admin.last_login
                              ? new Date(admin.last_login * 1000).toLocaleDateString()
                              : 'Never'}
                          </TableCell>
                          <TableCell>
                            <IconButton size="small">
                              <EditIcon />
                            </IconButton>
                            <IconButton size="small" color="error">
                              <DeleteIcon />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            </TabPanel>
          )}

          {/* System Logs Tab */}
          <TabPanel value={tabValue} index={hasPermission('manage_admins') && hasPermission('manage_bot_settings') ? 2 : 1}>
            <Box sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ mb: 2 }}>
                System Activity Logs
              </Typography>
              <Alert severity="info">
                System logs functionality will be implemented here.
                This will show admin actions, system events, and audit trails.
              </Alert>
            </Box>
          </TabPanel>
        </Card>
      )}

      {/* Create Admin Dialog */}
      <Dialog open={createAdminOpen} onClose={() => setCreateAdminOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add New Admin</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
            <TextField
              label="Admin ID"
              type="number"
              value={newAdmin.admin_id}
              onChange={(e) => setNewAdmin(prev => ({ ...prev, admin_id: e.target.value }))}
              fullWidth
            />
            <TextField
              label="Role"
              select
              value={newAdmin.role}
              onChange={(e) => setNewAdmin(prev => ({ ...prev, role: e.target.value }))}
              fullWidth
              SelectProps={{ native: true }}
            >
              <option value="admin">Admin</option>
              <option value="moderator">Moderator</option>
            </TextField>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateAdminOpen(false)}>Cancel</Button>
          <Button onClick={createAdmin} variant="contained">Create</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default AdminPage;
