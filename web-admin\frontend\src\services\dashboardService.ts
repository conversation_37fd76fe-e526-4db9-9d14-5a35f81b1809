import api from './authService';
import { DashboardStats, QuickAction, TrendData } from '../types';

export const dashboardService = {
  async getStats(): Promise<DashboardStats> {
    const response = await api.get('/dashboard/stats');
    return response.data;
  },

  async getRecentActivity(limit: number = 20): Promise<{
    recent_users: any[];
    recent_withdrawals: any[];
    timestamp: number;
  }> {
    const response = await api.get(`/dashboard/recent-activity?limit=${limit}`);
    return response.data;
  },

  async getSystemHealth(): Promise<{
    database: { status: string; connected: boolean };
    redis: { status: string; connected: boolean };
    websocket: any;
    timestamp: number;
  }> {
    const response = await api.get('/dashboard/system-health');
    return response.data;
  },

  async getAnalyticsTrends(period: '1d' | '7d' | '30d' = '7d'): Promise<TrendData> {
    const response = await api.get(`/dashboard/analytics/trends?period=${period}`);
    return response.data;
  },

  async getQuickActions(): Promise<{
    quick_actions: QuickAction[];
    timestamp: number;
  }> {
    const response = await api.get('/dashboard/quick-actions');
    return response.data;
  },
};
