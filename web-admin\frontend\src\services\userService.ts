import api from './authService';
import { User, UserSearchParams, PaginatedResponse } from '../types';

export const userService = {
  async searchUsers(params: UserSearchParams): Promise<PaginatedResponse<User>> {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, value.toString());
      }
    });

    const response = await api.get(`/users/?${searchParams.toString()}`);
    return {
      items: response.data.users,
      total: response.data.total,
      page: response.data.page,
      page_size: response.data.page_size,
      total_pages: response.data.total_pages,
    };
  },

  async getUserById(userId: number): Promise<User> {
    const response = await api.get(`/users/${userId}`);
    return response.data;
  },

  async updateUser(userId: number, updates: Partial<User>): Promise<User> {
    const response = await api.put(`/users/${userId}`, updates);
    return response.data;
  },

  async banUser(userId: number): Promise<{ message: string }> {
    const response = await api.post(`/users/${userId}/ban`);
    return response.data;
  },

  async unbanUser(userId: number): Promise<{ message: string }> {
    const response = await api.post(`/users/${userId}/unban`);
    return response.data;
  },

  async updateUserBalance(
    userId: number,
    amount: number,
    operation: 'credit' | 'debit',
    reason: string
  ): Promise<{
    message: string;
    old_balance: number;
    new_balance: number;
    amount: number;
    operation: string;
  }> {
    const response = await api.post(`/users/${userId}/balance`, {
      amount,
      operation,
      reason,
    });
    return response.data;
  },

  async getTopUsersByBalance(limit: number = 10): Promise<User[]> {
    const response = await api.get(`/users/stats/top-balance?limit=${limit}`);
    return response.data;
  },

  async getTopUsersByReferrals(limit: number = 10): Promise<any[]> {
    const response = await api.get(`/users/stats/top-referrals?limit=${limit}`);
    return response.data;
  },

  async getUserReferrals(
    userId: number,
    page: number = 1,
    pageSize: number = 20
  ): Promise<PaginatedResponse<User>> {
    const response = await api.get(
      `/users/${userId}/referrals?page=${page}&page_size=${pageSize}`
    );
    return {
      items: response.data.users,
      total: response.data.total,
      page: response.data.page,
      page_size: response.data.page_size,
      total_pages: response.data.total_pages,
    };
  },
};
