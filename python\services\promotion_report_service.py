"""
Promotion Report Service
Handles promotion report data retrieval and pagination
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from config.database import get_collection, COLLECTIONS
from services.user_service import UserService

logger = logging.getLogger(__name__)

class PromotionReportService:
    """Service for promotion report operations with pagination"""
    
    def __init__(self):
        self.user_service = UserService()
    
    async def get_user_referrals_paginated(
        self, 
        user_id: int, 
        page: int = 1, 
        per_page: int = 10
    ) -> Dict[str, Any]:
        """
        Get paginated list of users referred by this user
        
        Args:
            user_id: ID of the user whose referrals to fetch
            page: Page number (1-based)
            per_page: Number of items per page
            
        Returns:
            Dict containing referrals data, pagination info, and metadata
        """
        try:
            # Get user data to access promotion_report (PHP legacy data)
            user = await self.user_service.get_user(user_id)
            if not user:
                return {
                    'success': False,
                    'error': 'User not found',
                    'referrals': [],
                    'pagination': {
                        'current_page': 1,
                        'total_pages': 1,
                        'per_page': per_page,
                        'total_referrals': 0,
                        'has_next': False,
                        'has_previous': False
                    }
                }

            # Get promotion_report array (contains referral data from PHP migration)
            promotion_report = user.get('promotion_report', [])
            total_referrals = len(promotion_report)

            # Calculate pagination
            total_pages = max(1, (total_referrals + per_page - 1) // per_page)
            page = max(1, min(page, total_pages))

            # Calculate slice indices for pagination
            start_idx = (page - 1) * per_page
            end_idx = start_idx + per_page
            page_referrals = promotion_report[start_idx:end_idx]

            # Format referrals for display
            formatted_referrals = []
            start_number = start_idx + 1

            for i, referral in enumerate(page_referrals):
                ref_user_id = referral.get('referred_user_id', 0)
                ref_user_name = referral.get('referred_user_name', 'Unknown')
                amount_got = referral.get('amount_got', 0)

                # Get proper username display by looking up the actual user data
                username_display = await self._get_proper_username_display(ref_user_id, ref_user_name)

                formatted_referrals.append({
                    'number': start_number + i,
                    'user_id': ref_user_id,
                    'first_name': ref_user_name,
                    'username_display': username_display,
                    'amount_got': amount_got  # Include earnings for display
                })

            return {
                'success': True,
                'referrals': formatted_referrals,
                'pagination': {
                    'current_page': page,
                    'total_pages': total_pages,
                    'per_page': per_page,
                    'total_referrals': total_referrals,
                    'has_next': page < total_pages,
                    'has_previous': page > 1
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting paginated referrals for user {user_id}: {e}")
            return {
                'success': False,
                'error': str(e),
                'referrals': [],
                'pagination': {
                    'current_page': 1,
                    'total_pages': 1,
                    'per_page': per_page,
                    'total_referrals': 0,
                    'has_next': False,
                    'has_previous': False
                }
            }
    
    async def get_referral_summary(self, user_id: int) -> Dict[str, Any]:
        """
        Get referral summary statistics for a user
        Uses promotion_report array for compatibility with migrated PHP data

        Args:
            user_id: ID of the user

        Returns:
            Dict containing referral statistics
        """
        try:
            # Get user data to access promotion_report
            user = await self.user_service.get_user(user_id)
            if not user:
                return {
                    'total_referrals': 0,
                    'total_earnings': 0
                }

            # Use promotion_report array (PHP legacy data) as primary source
            promotion_report = user.get('promotion_report', [])
            total_referrals = len(promotion_report)
            total_earnings = sum(report.get('amount_got', 0) for report in promotion_report)

            # Calculate total subordinates (complete referral chain)
            total_subordinates = await self._calculate_total_subordinates(user_id)

            return {
                'total_referrals': total_referrals,
                'total_earnings': total_earnings,
                'total_subordinates': total_subordinates,
                'promotion_report_count': total_referrals
            }
            
        except Exception as e:
            logger.error(f"Error getting referral summary for user {user_id}: {e}")
            return {
                'total_referrals': 0,
                'total_earnings': 0,
                'total_subordinates': 0,
                'promotion_report_count': 0
            }

    async def _calculate_total_subordinates(self, user_id: int, visited: set = None) -> int:
        """
        Calculate total subordinates in the complete referral chain

        Args:
            user_id: ID of the user to calculate subordinates for
            visited: Set of visited user IDs to prevent infinite loops

        Returns:
            Total count of all users in the referral chain beneath this user
        """
        try:
            if visited is None:
                visited = set()

            # Prevent infinite loops
            if user_id in visited:
                return 0

            visited.add(user_id)

            # Get user data to access promotion_report (direct referrals)
            user = await self.user_service.get_user(user_id)
            if not user:
                return 0

            promotion_report = user.get('promotion_report', [])
            direct_referrals_count = len(promotion_report)

            # If no direct referrals, return 0
            if direct_referrals_count == 0:
                return 0

            # Start with direct referrals count
            total_subordinates = direct_referrals_count

            # Recursively calculate subordinates for each direct referral
            for referral in promotion_report:
                referred_user_id = referral.get('referred_user_id')
                if referred_user_id and referred_user_id not in visited:
                    # Add subordinates of this referral to the total
                    subordinates_of_referral = await self._calculate_total_subordinates(
                        referred_user_id, visited.copy()
                    )
                    total_subordinates += subordinates_of_referral

            return total_subordinates

        except Exception as e:
            logger.error(f"Error calculating total subordinates for user {user_id}: {e}")
            return 0

    async def _get_proper_username_display(self, user_id: int, fallback_name: str = 'Unknown') -> str:
        """
        Get proper username display format for referral lists

        Args:
            user_id: ID of the user to look up
            fallback_name: Fallback name from promotion_report

        Returns:
            Properly formatted display name in brackets
        """
        try:
            # Try to get the actual user data to find their real username
            user_data = await self.user_service.get_user(user_id)

            if user_data:
                username = user_data.get('username', '').strip()
                first_name = user_data.get('first_name', '').strip()

                # Apply the requested logic:
                # 1. If user has username: Display @username in brackets
                if username:
                    return f"@{username}"

                # 2. If no username but has first_name: Display first name in brackets
                elif first_name:
                    return first_name

                # 3. If neither exists: Use fallback from promotion_report
                elif fallback_name and fallback_name != 'Unknown':
                    return fallback_name

            # 4. Final fallback: Display "User"
            return "User"

        except Exception as e:
            logger.error(f"Error getting username display for user {user_id}: {e}")
            # Use fallback name if available, otherwise "User"
            if fallback_name and fallback_name != 'Unknown':
                return fallback_name
            return "User"
