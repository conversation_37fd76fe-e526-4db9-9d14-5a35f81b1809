"""
Referral service for Web Admin Panel
Handles referral tree visualization and deep chain analysis
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from pymongo import ASCENDING, DESCENDING

from app.core.database import database_manager
from app.core.redis_client import redis_client
from app.core.config import settings
from app.models.referral import (
    ReferralNode, ReferralTree, ReferralStats, ReferralChain,
    ReferralSearchRequest, ReferralPerformance, ReferralAnalytics
)

logger = logging.getLogger(__name__)


class ReferralService:
    """Service for referral management and visualization"""
    
    def __init__(self):
        self.db = database_manager
        self.cache = redis_client
    
    async def get_referral_tree(self, root_user_id: int, max_depth: int = None) -> ReferralTree:
        """Get complete referral tree for a user with optimized loading"""
        try:
            if max_depth is None:
                max_depth = settings.MAX_REFERRAL_DEPTH
            
            # Check cache first
            cache_key = f"referral_tree:{root_user_id}:{max_depth}"
            cached_tree = await self.cache.get(cache_key)
            if cached_tree:
                return ReferralTree(**cached_tree)
            
            # Get root user
            root_user = await self.db.users.find_one({"user_id": root_user_id})
            if not root_user:
                raise ValueError(f"User {root_user_id} not found")
            
            # Build tree recursively with batching for performance
            root_node = await self._build_referral_node(root_user, 0, max_depth)
            
            # Calculate tree statistics
            total_nodes = await self._count_total_referrals(root_user_id, max_depth)
            actual_max_depth = await self._get_max_depth(root_user_id)
            total_earnings = await self._calculate_total_earnings(root_user_id)
            
            tree = ReferralTree(
                root_user=root_node,
                total_nodes=total_nodes,
                max_depth=min(actual_max_depth, max_depth),
                total_earnings=total_earnings
            )
            
            # Cache for 5 minutes
            await self.cache.set(cache_key, tree.dict(), ttl=300)
            
            return tree
            
        except Exception as e:
            logger.error(f"Error getting referral tree for user {root_user_id}: {e}")
            raise
    
    async def _build_referral_node(self, user_data: Dict, current_depth: int, max_depth: int) -> ReferralNode:
        """Build a single referral node with its children"""
        try:
            # Create base node
            node = ReferralNode(
                user_id=user_data["user_id"],
                first_name=user_data.get("first_name", ""),
                username=user_data.get("username", ""),
                balance=user_data.get("balance", 0),
                level=current_depth,
                referred_by=user_data.get("referred_by", "None"),
                created_at=user_data.get("created_at", 0),
                children=[]
            )
            
            # If we haven't reached max depth, get children
            if current_depth < max_depth:
                children = await self._get_direct_referrals(user_data["user_id"])
                
                # Process children in batches for better performance
                batch_size = settings.REFERRAL_BATCH_SIZE
                for i in range(0, len(children), batch_size):
                    batch = children[i:i + batch_size]
                    
                    # Build child nodes
                    for child_data in batch:
                        child_node = await self._build_referral_node(
                            child_data, current_depth + 1, max_depth
                        )
                        node.children.append(child_node)
            
            # Calculate node statistics
            node.direct_referrals = len(node.children)
            node.total_referrals = await self._count_total_referrals(user_data["user_id"], max_depth - current_depth)
            
            return node
            
        except Exception as e:
            logger.error(f"Error building referral node for user {user_data.get('user_id')}: {e}")
            raise
    
    async def _get_direct_referrals(self, user_id: int) -> List[Dict]:
        """Get direct referrals for a user"""
        try:
            cursor = self.db.users.find(
                {"referred_by": str(user_id)},
                {
                    "user_id": 1,
                    "first_name": 1,
                    "username": 1,
                    "balance": 1,
                    "referred_by": 1,
                    "created_at": 1
                }
            ).sort("created_at", DESCENDING)
            
            return await cursor.to_list(length=None)
            
        except Exception as e:
            logger.error(f"Error getting direct referrals for user {user_id}: {e}")
            return []
    
    async def _count_total_referrals(self, user_id: int, max_depth: int) -> int:
        """Count total referrals in the chain up to max_depth"""
        try:
            # Use aggregation pipeline for efficient counting
            pipeline = []
            
            # Start with direct referrals
            match_stage = {"referred_by": str(user_id)}
            
            # Build recursive lookup for deep counting
            # This is a simplified version - in production, you might want to use $graphLookup
            total_count = 0
            current_level_users = [user_id]
            
            for depth in range(max_depth):
                if not current_level_users:
                    break
                
                # Get next level referrals
                next_level = await self.db.users.find(
                    {"referred_by": {"$in": [str(uid) for uid in current_level_users]}},
                    {"user_id": 1}
                ).to_list(length=None)
                
                level_count = len(next_level)
                total_count += level_count
                
                # Prepare for next iteration
                current_level_users = [user["user_id"] for user in next_level]
            
            return total_count
            
        except Exception as e:
            logger.error(f"Error counting total referrals for user {user_id}: {e}")
            return 0
    
    async def _get_max_depth(self, user_id: int) -> int:
        """Get maximum depth of referral chain"""
        try:
            max_depth = 0
            current_level_users = [user_id]
            
            # Traverse until no more referrals found
            while current_level_users and max_depth < settings.MAX_REFERRAL_DEPTH:
                next_level = await self.db.users.find(
                    {"referred_by": {"$in": [str(uid) for uid in current_level_users]}},
                    {"user_id": 1}
                ).to_list(length=None)
                
                if not next_level:
                    break
                
                max_depth += 1
                current_level_users = [user["user_id"] for user in next_level]
            
            return max_depth
            
        except Exception as e:
            logger.error(f"Error getting max depth for user {user_id}: {e}")
            return 0
    
    async def _calculate_total_earnings(self, user_id: int) -> int:
        """Calculate total earnings from referral chain"""
        try:
            # This would depend on your referral commission structure
            # For now, return 0 as placeholder
            return 0
            
        except Exception as e:
            logger.error(f"Error calculating total earnings for user {user_id}: {e}")
            return 0
    
    async def get_referral_chain(self, user_id: int) -> ReferralChain:
        """Get the complete referral chain leading to a user"""
        try:
            chain = []
            current_user_id = user_id
            total_depth = 0
            
            # Traverse up the referral chain
            while current_user_id and total_depth < settings.MAX_REFERRAL_DEPTH:
                user_data = await self.db.users.find_one({"user_id": current_user_id})
                if not user_data:
                    break
                
                node = ReferralNode(
                    user_id=user_data["user_id"],
                    first_name=user_data.get("first_name", ""),
                    username=user_data.get("username", ""),
                    balance=user_data.get("balance", 0),
                    level=total_depth,
                    referred_by=user_data.get("referred_by", "None"),
                    created_at=user_data.get("created_at", 0)
                )
                
                chain.append(node)
                
                # Move to referrer
                referred_by = user_data.get("referred_by", "None")
                if referred_by == "None" or not referred_by.isdigit():
                    break
                
                current_user_id = int(referred_by)
                total_depth += 1
            
            return ReferralChain(
                user_id=user_id,
                chain=list(reversed(chain)),  # Reverse to show from top referrer
                total_depth=total_depth,
                total_earnings=0  # Calculate based on your commission structure
            )
            
        except Exception as e:
            logger.error(f"Error getting referral chain for user {user_id}: {e}")
            raise
    
    async def get_referral_stats(self) -> ReferralStats:
        """Get comprehensive referral system statistics"""
        try:
            # Check cache first
            cache_key = "referral_stats"
            cached_stats = await self.cache.get(cache_key)
            if cached_stats:
                return ReferralStats(**cached_stats)
            
            # Calculate statistics
            pipeline = [
                {
                    "$facet": {
                        "total_referrals": [
                            {"$match": {"referred_by": {"$ne": "None"}}},
                            {"$count": "count"}
                        ],
                        "active_referrers": [
                            {
                                "$lookup": {
                                    "from": "users",
                                    "localField": "user_id",
                                    "foreignField": "referred_by",
                                    "as": "referrals"
                                }
                            },
                            {"$match": {"referrals": {"$ne": []}}},
                            {"$count": "count"}
                        ],
                        "top_referrers": [
                            {
                                "$lookup": {
                                    "from": "users",
                                    "localField": "user_id",
                                    "foreignField": "referred_by",
                                    "as": "referrals"
                                }
                            },
                            {
                                "$addFields": {
                                    "referral_count": {"$size": "$referrals"}
                                }
                            },
                            {"$match": {"referral_count": {"$gt": 0}}},
                            {"$sort": {"referral_count": -1}},
                            {"$limit": 10},
                            {
                                "$project": {
                                    "user_id": 1,
                                    "first_name": 1,
                                    "username": 1,
                                    "referral_count": 1
                                }
                            }
                        ]
                    }
                }
            ]
            
            result = await self.db.users.aggregate(pipeline).to_list(1)
            if not result:
                return ReferralStats(
                    total_referrals=0, active_referrers=0, total_referral_earnings=0,
                    average_referrals_per_user=0.0, top_referrers=[],
                    referral_levels_distribution={}, daily_referrals=[],
                    weekly_referrals=[], monthly_referrals=[]
                )
            
            data = result[0]
            
            total_referrals = data["total_referrals"][0]["count"] if data["total_referrals"] else 0
            active_referrers = data["active_referrers"][0]["count"] if data["active_referrers"] else 0
            
            stats = ReferralStats(
                total_referrals=total_referrals,
                active_referrers=active_referrers,
                total_referral_earnings=0,  # Calculate based on your system
                average_referrals_per_user=total_referrals / max(active_referrers, 1),
                top_referrers=data["top_referrers"],
                referral_levels_distribution={},  # Calculate if needed
                daily_referrals=[],  # Calculate trend data
                weekly_referrals=[],
                monthly_referrals=[]
            )
            
            # Cache for 5 minutes
            await self.cache.set(cache_key, stats.dict(), ttl=300)
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting referral stats: {e}")
            raise
    
    async def search_referral_performers(self, search_request: ReferralSearchRequest) -> List[ReferralPerformance]:
        """Search for top referral performers"""
        try:
            # Build aggregation pipeline
            pipeline = [
                {
                    "$lookup": {
                        "from": "users",
                        "localField": "user_id",
                        "foreignField": "referred_by",
                        "as": "direct_referrals"
                    }
                },
                {
                    "$addFields": {
                        "direct_referrals_count": {"$size": "$direct_referrals"}
                    }
                }
            ]
            
            # Add filters
            match_conditions = {}
            
            if search_request.user_id:
                match_conditions["user_id"] = search_request.user_id
            
            if search_request.min_referrals:
                match_conditions["direct_referrals_count"] = {"$gte": search_request.min_referrals}
            
            if match_conditions:
                pipeline.append({"$match": match_conditions})
            
            # Sort and paginate
            pipeline.extend([
                {"$sort": {"direct_referrals_count": -1}},
                {"$skip": (search_request.page - 1) * search_request.page_size},
                {"$limit": search_request.page_size}
            ])
            
            # Project final fields
            pipeline.append({
                "$project": {
                    "user_id": 1,
                    "first_name": 1,
                    "username": 1,
                    "direct_referrals": "$direct_referrals_count",
                    "total_referrals": "$direct_referrals_count",  # Simplified
                    "max_depth": 1,  # Would need recursive calculation
                    "total_earnings": 0,  # Calculate based on your system
                    "conversion_rate": 0.0,  # Calculate based on your metrics
                    "activity_score": 0.0  # Calculate based on your metrics
                }
            })
            
            result = await self.db.users.aggregate(pipeline).to_list(search_request.page_size)
            
            return [ReferralPerformance(**item) for item in result]
            
        except Exception as e:
            logger.error(f"Error searching referral performers: {e}")
            raise


# Global referral service instance
referral_service = ReferralService()
