import api from './authService';
import { Withdrawal, WithdrawalSearchParams, WithdrawalStats, WithdrawalMethodStats, PaginatedResponse } from '../types';

export const withdrawalService = {
  async searchWithdrawals(params: WithdrawalSearchParams): Promise<PaginatedResponse<Withdrawal>> {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, value.toString());
      }
    });

    const response = await api.get(`/withdrawals/?${searchParams.toString()}`);
    return {
      items: response.data.withdrawals,
      total: response.data.total,
      page: response.data.page,
      page_size: response.data.page_size,
      total_pages: response.data.total_pages,
    };
  },

  async getWithdrawalById(withdrawalId: string): Promise<Withdrawal> {
    const response = await api.get(`/withdrawals/${withdrawalId}`);
    return response.data;
  },

  async updateWithdrawal(
    withdrawalId: string,
    updates: { status?: string; admin_note?: string }
  ): Promise<Withdrawal> {
    const response = await api.put(`/withdrawals/${withdrawalId}`, updates);
    return response.data;
  },

  async approveWithdrawal(
    withdrawalId: string,
    adminNote?: string
  ): Promise<{ message: string }> {
    const response = await api.post(`/withdrawals/${withdrawalId}/approve`, {
      admin_note: adminNote,
    });
    return response.data;
  },

  async rejectWithdrawal(
    withdrawalId: string,
    adminNote: string
  ): Promise<{ message: string }> {
    const response = await api.post(`/withdrawals/${withdrawalId}/reject`, {
      admin_note: adminNote,
    });
    return response.data;
  },

  async completeWithdrawal(
    withdrawalId: string,
    adminNote?: string
  ): Promise<{ message: string }> {
    const response = await api.post(`/withdrawals/${withdrawalId}/complete`, {
      admin_note: adminNote,
    });
    return response.data;
  },

  async bulkAction(
    withdrawalIds: string[],
    action: 'approve' | 'reject' | 'complete',
    adminNote?: string
  ): Promise<{
    message: string;
    updated_count: number;
    failed_count: number;
    failed_ids: string[];
  }> {
    const response = await api.post('/withdrawals/bulk-action', {
      withdrawal_ids: withdrawalIds,
      action,
      admin_note: adminNote,
    });
    return response.data;
  },

  async getWithdrawalStats(): Promise<WithdrawalStats> {
    const response = await api.get('/withdrawals/stats/overview');
    return response.data;
  },

  async getWithdrawalMethodStats(): Promise<WithdrawalMethodStats[]> {
    const response = await api.get('/withdrawals/stats/by-method');
    return response.data;
  },
};
