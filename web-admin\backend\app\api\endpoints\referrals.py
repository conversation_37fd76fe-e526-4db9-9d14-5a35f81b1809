"""
Referral management endpoints for Web Admin Panel
"""

import logging
from typing import Optional
from fastapi import APIRouter, HTTPException, status, Depends, Query

from app.core.auth import require_permission
from app.services.referral_service import referral_service
from app.models.referral import ReferralSearchRequest

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/stats")
async def get_referral_stats(
    current_admin: dict = Depends(require_permission("manage_referrals"))
):
    """Get comprehensive referral system statistics"""
    try:
        stats = await referral_service.get_referral_stats()
        return stats.dict()

    except Exception as e:
        logger.error(f"Error getting referral stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving referral statistics"
        )


@router.get("/tree/{user_id}")
async def get_referral_tree(
    user_id: int,
    max_depth: Optional[int] = Query(None, description="Maximum depth to traverse"),
    current_admin: dict = Depends(require_permission("manage_referrals"))
):
    """Get interactive referral tree for a user"""
    try:
        tree = await referral_service.get_referral_tree(user_id, max_depth)
        return tree.dict()

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error getting referral tree for user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving referral tree"
        )


@router.get("/chain/{user_id}")
async def get_referral_chain(
    user_id: int,
    current_admin: dict = Depends(require_permission("manage_referrals"))
):
    """Get referral chain leading to a user"""
    try:
        chain = await referral_service.get_referral_chain(user_id)
        return chain.dict()

    except Exception as e:
        logger.error(f"Error getting referral chain for user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving referral chain"
        )


@router.get("/performers")
async def get_referral_performers(
    user_id: Optional[int] = Query(None, description="Filter by specific user"),
    min_referrals: Optional[int] = Query(None, description="Minimum referrals filter"),
    max_depth: Optional[int] = Query(None, description="Maximum depth filter"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Page size"),
    current_admin: dict = Depends(require_permission("manage_referrals"))
):
    """Get top referral performers"""
    try:
        search_request = ReferralSearchRequest(
            user_id=user_id,
            min_referrals=min_referrals,
            max_depth=max_depth,
            page=page,
            page_size=page_size
        )

        performers = await referral_service.search_referral_performers(search_request)
        return [performer.dict() for performer in performers]

    except Exception as e:
        logger.error(f"Error getting referral performers: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving referral performers"
        )


@router.get("/analytics")
async def get_referral_analytics(
    period: str = Query("7d", regex="^(1d|7d|30d)$", description="Analytics period"),
    current_admin: dict = Depends(require_permission("view_analytics"))
):
    """Get referral analytics and trends"""
    try:
        # This would be implemented with more detailed analytics
        # For now, return basic structure
        return {
            "period": period,
            "total_referrals": 0,
            "growth_rate": 0.0,
            "conversion_rate": 0.0,
            "trend_data": [],
            "top_performers": [],
            "message": "Detailed analytics implementation in progress"
        }

    except Exception as e:
        logger.error(f"Error getting referral analytics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving referral analytics"
        )
