version: '3.8'

services:
  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: telegram-bot-admin-backend-prod
    restart: unless-stopped
    environment:
      - DEBUG=false
      - HOST=0.0.0.0
      - PORT=8000
      - MONGODB_URI=${MONGODB_URI}
      - DATABASE_NAME=${DATABASE_NAME}
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY=${SECRET_KEY}
      - ADMIN_IDS=${ADMIN_IDS}
      - SUPER_ADMIN_ID=${SUPER_ADMIN_ID}
      - BOT_TOKEN=${BOT_TOKEN}
      - BOT_USERNAME=${BOT_USERNAME}
      - LOG_LEVEL=INFO
    depends_on:
      - redis
    volumes:
      - ./backend/logs:/app/logs
      - ./backend/uploads:/app/uploads
    networks:
      - telegram-bot-network
    healthcheck:
      test: ["<PERSON><PERSON>", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    container_name: telegram-bot-admin-frontend-prod
    restart: unless-stopped
    environment:
      - REACT_APP_API_URL=${FRONTEND_API_URL}
      - REACT_APP_WS_URL=${FRONTEND_WS_URL}
    depends_on:
      - backend
    networks:
      - telegram-bot-network

  # Redis for caching and real-time features
  redis:
    image: redis:7-alpine
    container_name: telegram-bot-redis-prod
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - telegram-bot-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx reverse proxy and load balancer
  nginx:
    image: nginx:alpine
    container_name: telegram-bot-nginx-prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./nginx/logs:/var/log/nginx
      - nginx_cache:/var/cache/nginx
    depends_on:
      - frontend
      - backend
    networks:
      - telegram-bot-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: telegram-bot-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - telegram-bot-network

  # Grafana for monitoring dashboards (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: telegram-bot-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - telegram-bot-network

  # Log aggregation with Loki (optional)
  loki:
    image: grafana/loki:latest
    container_name: telegram-bot-loki
    restart: unless-stopped
    ports:
      - "3100:3100"
    volumes:
      - ./monitoring/loki.yml:/etc/loki/local-config.yaml
      - loki_data:/loki
    command: -config.file=/etc/loki/local-config.yaml
    networks:
      - telegram-bot-network

  # Backup service
  backup:
    image: alpine:latest
    container_name: telegram-bot-backup
    restart: unless-stopped
    environment:
      - MONGODB_URI=${MONGODB_URI}
      - BACKUP_SCHEDULE=${BACKUP_SCHEDULE:-0 2 * * *}
      - BACKUP_RETENTION_DAYS=${BACKUP_RETENTION_DAYS:-30}
    volumes:
      - ./backups:/backups
      - ./scripts/backup.sh:/backup.sh
    command: crond -f
    networks:
      - telegram-bot-network

volumes:
  redis_data:
  nginx_cache:
  prometheus_data:
  grafana_data:
  loki_data:

networks:
  telegram-bot-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
