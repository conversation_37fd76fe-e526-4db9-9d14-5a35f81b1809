"""
Withdrawal service for Web Admin Panel
Handles all withdrawal-related database operations
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from pymongo import ASCENDING, DESCENDING

from app.core.database import database_manager
from app.core.redis_client import redis_client
from app.core.config import settings
from app.models.withdrawal import (
    Withdrawal, WithdrawalCreate, WithdrawalUpdate, WithdrawalStats,
    WithdrawalListResponse, WithdrawalSearchRequest, WithdrawalBulkAction,
    WithdrawalMethodStats, WithdrawalTrend
)

logger = logging.getLogger(__name__)


class WithdrawalService:
    """Service for withdrawal management operations"""
    
    def __init__(self):
        self.db = database_manager
        self.cache = redis_client
    
    async def get_withdrawal_stats(self) -> WithdrawalStats:
        """Get comprehensive withdrawal statistics"""
        try:
            # Check cache first
            cache_key = "withdrawal_stats"
            cached_stats = await self.cache.get(cache_key)
            if cached_stats:
                return WithdrawalStats(**cached_stats)
            
            # Calculate current timestamps
            now = datetime.now()
            today_start = int(now.replace(hour=0, minute=0, second=0, microsecond=0).timestamp())
            week_start = int((now - timedelta(days=7)).timestamp())
            month_start = int((now - timedelta(days=30)).timestamp())
            
            # Aggregate withdrawal statistics
            pipeline = [
                {
                    "$facet": {
                        "status_stats": [
                            {
                                "$group": {
                                    "_id": "$status",
                                    "count": {"$sum": 1},
                                    "total_amount": {"$sum": "$amount"}
                                }
                            }
                        ],
                        "today_stats": [
                            {"$match": {"created_at": {"$gte": today_start}}},
                            {
                                "$group": {
                                    "_id": None,
                                    "count": {"$sum": 1},
                                    "total_amount": {"$sum": "$amount"}
                                }
                            }
                        ],
                        "week_stats": [
                            {"$match": {"created_at": {"$gte": week_start}}},
                            {
                                "$group": {
                                    "_id": None,
                                    "count": {"$sum": 1},
                                    "total_amount": {"$sum": "$amount"}
                                }
                            }
                        ],
                        "month_stats": [
                            {"$match": {"created_at": {"$gte": month_start}}},
                            {
                                "$group": {
                                    "_id": None,
                                    "count": {"$sum": 1},
                                    "total_amount": {"$sum": "$amount"}
                                }
                            }
                        ],
                        "total_stats": [
                            {
                                "$group": {
                                    "_id": None,
                                    "total_count": {"$sum": 1},
                                    "total_amount": {"$sum": "$amount"}
                                }
                            }
                        ]
                    }
                }
            ]
            
            result = await self.db.withdrawals.aggregate(pipeline).to_list(1)
            if not result:
                return WithdrawalStats(
                    total_withdrawals=0, pending_withdrawals=0, approved_withdrawals=0,
                    rejected_withdrawals=0, completed_withdrawals=0,
                    total_amount=0, pending_amount=0, approved_amount=0, completed_amount=0,
                    today_withdrawals=0, today_amount=0,
                    this_week_withdrawals=0, this_week_amount=0,
                    this_month_withdrawals=0, this_month_amount=0
                )
            
            data = result[0]
            
            # Process status statistics
            status_counts = {item["_id"]: item["count"] for item in data["status_stats"]}
            status_amounts = {item["_id"]: item["total_amount"] for item in data["status_stats"]}
            
            stats = WithdrawalStats(
                total_withdrawals=data["total_stats"][0]["total_count"] if data["total_stats"] else 0,
                pending_withdrawals=status_counts.get("Under review", 0),
                approved_withdrawals=status_counts.get("Approved", 0),
                rejected_withdrawals=status_counts.get("Rejected", 0),
                completed_withdrawals=status_counts.get("Completed", 0),
                total_amount=data["total_stats"][0]["total_amount"] if data["total_stats"] else 0,
                pending_amount=status_amounts.get("Under review", 0),
                approved_amount=status_amounts.get("Approved", 0),
                completed_amount=status_amounts.get("Completed", 0),
                today_withdrawals=data["today_stats"][0]["count"] if data["today_stats"] else 0,
                today_amount=data["today_stats"][0]["total_amount"] if data["today_stats"] else 0,
                this_week_withdrawals=data["week_stats"][0]["count"] if data["week_stats"] else 0,
                this_week_amount=data["week_stats"][0]["total_amount"] if data["week_stats"] else 0,
                this_month_withdrawals=data["month_stats"][0]["count"] if data["month_stats"] else 0,
                this_month_amount=data["month_stats"][0]["total_amount"] if data["month_stats"] else 0
            )
            
            # Cache for 1 minute
            await self.cache.set(cache_key, stats.dict(), ttl=settings.STATS_CACHE_TTL)
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting withdrawal stats: {e}")
            raise
    
    async def search_withdrawals(self, search_request: WithdrawalSearchRequest) -> WithdrawalListResponse:
        """Search withdrawals with filters and pagination"""
        try:
            # Build query filter
            query_filter = {}
            
            if search_request.user_id:
                query_filter["user_id"] = search_request.user_id
            
            if search_request.status:
                query_filter["status"] = search_request.status
            
            if search_request.withdrawal_method:
                query_filter["withdrawal_method"] = search_request.withdrawal_method
            
            if search_request.min_amount is not None:
                query_filter.setdefault("amount", {})["$gte"] = search_request.min_amount
            
            if search_request.max_amount is not None:
                query_filter.setdefault("amount", {})["$lte"] = search_request.max_amount
            
            # Date range filtering
            if search_request.date_from or search_request.date_to:
                date_filter = {}
                if search_request.date_from:
                    date_from = datetime.fromisoformat(search_request.date_from.replace('Z', '+00:00'))
                    date_filter["$gte"] = int(date_from.timestamp())
                if search_request.date_to:
                    date_to = datetime.fromisoformat(search_request.date_to.replace('Z', '+00:00'))
                    date_filter["$lte"] = int(date_to.timestamp())
                query_filter["created_at"] = date_filter
            
            # Calculate pagination
            skip = (search_request.page - 1) * search_request.page_size
            limit = min(search_request.page_size, settings.MAX_PAGE_SIZE)
            
            # Determine sort order
            sort_direction = DESCENDING if search_request.sort_order == "desc" else ASCENDING
            
            # Get total count
            total = await self.db.withdrawals.count_documents(query_filter)
            
            # Get withdrawals
            cursor = self.db.withdrawals.find(query_filter).sort(
                search_request.sort_by, sort_direction
            ).skip(skip).limit(limit)
            
            withdrawals_data = await cursor.to_list(length=limit)
            withdrawals = [Withdrawal(**withdrawal_data) for withdrawal_data in withdrawals_data]
            
            total_pages = (total + search_request.page_size - 1) // search_request.page_size
            
            return WithdrawalListResponse(
                withdrawals=withdrawals,
                total=total,
                page=search_request.page,
                page_size=search_request.page_size,
                total_pages=total_pages
            )
            
        except Exception as e:
            logger.error(f"Error searching withdrawals: {e}")
            raise
    
    async def get_withdrawal_by_id(self, withdrawal_id: str) -> Optional[Withdrawal]:
        """Get withdrawal by ID"""
        try:
            withdrawal_data = await self.db.withdrawals.find_one({"withdrawal_id": withdrawal_id})
            if withdrawal_data:
                return Withdrawal(**withdrawal_data)
            return None
        except Exception as e:
            logger.error(f"Error getting withdrawal {withdrawal_id}: {e}")
            raise
    
    async def update_withdrawal(self, withdrawal_id: str, withdrawal_update: WithdrawalUpdate, admin_id: int) -> bool:
        """Update withdrawal status"""
        try:
            update_data = withdrawal_update.dict(exclude_unset=True)
            if not update_data:
                return True
            
            update_data["updated_at"] = self.db.get_current_timestamp()
            
            # If status is being updated to completed/approved, set processed_at
            if "status" in update_data and update_data["status"] in ["Completed", "Approved"]:
                update_data["processed_at"] = self.db.get_current_timestamp()
            
            result = await self.db.withdrawals.update_one(
                {"withdrawal_id": withdrawal_id},
                {"$set": update_data}
            )
            
            if result.modified_count > 0:
                # Log admin action
                await self._log_admin_action(
                    admin_id,
                    "update_withdrawal",
                    {
                        "withdrawal_id": withdrawal_id,
                        "updates": update_data
                    }
                )

                # Broadcast real-time update
                try:
                    from app.services.realtime_service import realtime_service
                    await realtime_service.broadcast_withdrawal_update(
                        withdrawal_id, "updated", {
                            "admin_id": admin_id,
                            "updates": update_data
                        }
                    )
                except Exception as e:
                    logger.error(f"Error broadcasting withdrawal update: {e}")

                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error updating withdrawal {withdrawal_id}: {e}")
            raise
    
    async def bulk_update_withdrawals(self, bulk_action: WithdrawalBulkAction) -> Dict[str, Any]:
        """Perform bulk action on withdrawals"""
        try:
            updated_count = 0
            failed_ids = []
            
            for withdrawal_id in bulk_action.withdrawal_ids:
                try:
                    update_data = {
                        "updated_at": self.db.get_current_timestamp()
                    }
                    
                    if bulk_action.action == "approve":
                        update_data["status"] = "Approved"
                        update_data["processed_at"] = self.db.get_current_timestamp()
                    elif bulk_action.action == "reject":
                        update_data["status"] = "Rejected"
                        update_data["processed_at"] = self.db.get_current_timestamp()
                    elif bulk_action.action == "complete":
                        update_data["status"] = "Completed"
                        update_data["processed_at"] = self.db.get_current_timestamp()
                    
                    if bulk_action.admin_note:
                        update_data["admin_note"] = bulk_action.admin_note
                    
                    result = await self.db.withdrawals.update_one(
                        {"withdrawal_id": withdrawal_id},
                        {"$set": update_data}
                    )
                    
                    if result.modified_count > 0:
                        updated_count += 1
                    else:
                        failed_ids.append(withdrawal_id)
                        
                except Exception as e:
                    logger.error(f"Error updating withdrawal {withdrawal_id}: {e}")
                    failed_ids.append(withdrawal_id)
            
            # Log bulk action
            await self._log_admin_action(
                bulk_action.admin_id,
                f"bulk_{bulk_action.action}_withdrawals",
                {
                    "withdrawal_ids": bulk_action.withdrawal_ids,
                    "updated_count": updated_count,
                    "failed_ids": failed_ids,
                    "admin_note": bulk_action.admin_note
                }
            )
            
            return {
                "updated_count": updated_count,
                "failed_count": len(failed_ids),
                "failed_ids": failed_ids
            }
            
        except Exception as e:
            logger.error(f"Error performing bulk withdrawal action: {e}")
            raise
    
    async def get_withdrawal_method_stats(self) -> List[WithdrawalMethodStats]:
        """Get statistics by withdrawal method"""
        try:
            pipeline = [
                {
                    "$group": {
                        "_id": "$withdrawal_method",
                        "count": {"$sum": 1},
                        "total_amount": {"$sum": "$amount"},
                        "pending_count": {
                            "$sum": {"$cond": [{"$eq": ["$status", "Under review"]}, 1, 0]}
                        },
                        "pending_amount": {
                            "$sum": {"$cond": [{"$eq": ["$status", "Under review"]}, "$amount", 0]}
                        },
                        "completed_count": {
                            "$sum": {"$cond": [{"$eq": ["$status", "Completed"]}, 1, 0]}
                        },
                        "completed_amount": {
                            "$sum": {"$cond": [{"$eq": ["$status", "Completed"]}, "$amount", 0]}
                        }
                    }
                }
            ]
            
            result = await self.db.withdrawals.aggregate(pipeline).to_list(None)
            
            return [
                WithdrawalMethodStats(
                    method=item["_id"],
                    count=item["count"],
                    total_amount=item["total_amount"],
                    pending_count=item["pending_count"],
                    pending_amount=item["pending_amount"],
                    completed_count=item["completed_count"],
                    completed_amount=item["completed_amount"]
                )
                for item in result
            ]
            
        except Exception as e:
            logger.error(f"Error getting withdrawal method stats: {e}")
            raise
    
    async def _log_admin_action(self, admin_id: int, action: str, metadata: Dict[str, Any]):
        """Log admin action"""
        try:
            log_entry = {
                "admin_id": admin_id,
                "action": action,
                "metadata": metadata,
                "timestamp": self.db.get_current_timestamp()
            }
            
            await self.db.admin_logs.insert_one(log_entry)
            
        except Exception as e:
            logger.error(f"Error logging admin action: {e}")


# Global withdrawal service instance
withdrawal_service = WithdrawalService()
