// Authentication types
export interface LoginRequest {
  admin_id: number;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
  admin_info: AdminInfo;
}

export interface AdminInfo {
  admin_id: number;
  role: string;
  permissions: AdminPermissions;
  last_login?: number;
  is_active: boolean;
  created_at: number;
  updated_at: number;
}

export interface AdminPermissions {
  view_dashboard: boolean;
  manage_users: boolean;
  manage_withdrawals: boolean;
  manage_referrals: boolean;
  manage_admins: boolean;
  manage_bot_settings: boolean;
  view_analytics: boolean;
  manage_broadcasts: boolean;
  manage_gift_codes: boolean;
  system_maintenance: boolean;
}

// User types
export interface User {
  user_id: number;
  first_name: string;
  last_name: string;
  username: string;
  banned: boolean;
  referred: boolean;
  referred_by: string;
  joining_bonus_got: number;
  referral_link: string;
  balance: number;
  successful_withdraw: number;
  withdraw_under_review: number;
  gift_claimed: boolean;
  claimed_levels: number[];
  account_info: AccountInfo;
  promotion_report: any[];
  withdrawal_reports: any[];
  created_at: number;
  updated_at: number;
}

export interface AccountInfo {
  name: string;
  ifsc: string;
  email: string;
  account_number: string;
  mobile_number: string;
  withdrawal_method: string;
  usdt_address: string;
  binance_id: string;
}

export interface UserStats {
  total_users: number;
  active_users: number;
  banned_users: number;
  new_users_today: number;
  new_users_this_week: number;
  new_users_this_month: number;
  total_balance: number;
  total_withdrawals: number;
  pending_withdrawals: number;
}

// Withdrawal types
export interface Withdrawal {
  withdrawal_id: string;
  user_id: number;
  username: string;
  first_name: string;
  amount: number;
  amount_with_tax: number;
  tax_deducted: number;
  withdrawal_method: string;
  status: string;
  created_at: number;
  updated_at: number;
  processed_at?: number;
  admin_note: string;
  account_info: AccountInfo;
}

export interface WithdrawalStats {
  total_withdrawals: number;
  pending_withdrawals: number;
  approved_withdrawals: number;
  rejected_withdrawals: number;
  completed_withdrawals: number;
  total_amount: number;
  pending_amount: number;
  approved_amount: number;
  completed_amount: number;
  today_withdrawals: number;
  today_amount: number;
  this_week_withdrawals: number;
  this_week_amount: number;
  this_month_withdrawals: number;
  this_month_amount: number;
}

// Dashboard types
export interface DashboardStats {
  user_stats: UserStats;
  withdrawal_stats: WithdrawalStats;
  withdrawal_method_stats: WithdrawalMethodStats[];
  top_users: {
    by_balance: User[];
    by_referrals: any[];
  };
  timestamp: number;
}

export interface WithdrawalMethodStats {
  method: string;
  count: number;
  total_amount: number;
  pending_count: number;
  pending_amount: number;
  completed_count: number;
  completed_amount: number;
}

export interface QuickAction {
  type: string;
  title: string;
  count: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
  action_url: string;
}

// API Response types
export interface ApiResponse<T> {
  data?: T;
  message?: string;
  error?: string;
  status: number;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}

// Search and filter types
export interface UserSearchParams {
  query?: string;
  banned?: boolean;
  has_balance?: boolean;
  has_withdrawals?: boolean;
  referred_by?: string;
  page?: number;
  page_size?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface WithdrawalSearchParams {
  user_id?: number;
  status?: string;
  withdrawal_method?: string;
  min_amount?: number;
  max_amount?: number;
  date_from?: string;
  date_to?: string;
  page?: number;
  page_size?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

// WebSocket types
export interface WebSocketMessage {
  type: string;
  data?: any;
  timestamp?: string;
}

// Chart data types
export interface ChartDataPoint {
  name: string;
  value: number;
  date?: string;
}

export interface TrendData {
  period: string;
  interval: string;
  user_registrations: ChartDataPoint[];
  withdrawals: ChartDataPoint[];
  timestamp: number;
}

// Theme types
export interface ThemeMode {
  mode: 'light' | 'dark';
}

// Navigation types
export interface NavigationItem {
  title: string;
  path: string;
  icon: React.ComponentType;
  permission?: keyof AdminPermissions;
  children?: NavigationItem[];
}

// Form types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'number' | 'email' | 'password' | 'select' | 'checkbox';
  required?: boolean;
  options?: { value: string | number; label: string }[];
  validation?: (value: any) => string | undefined;
}

// Error types
export interface AppError {
  message: string;
  code?: string;
  details?: any;
}
