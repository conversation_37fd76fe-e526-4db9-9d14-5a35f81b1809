{"name": "telegram-bot-admin-panel", "version": "1.0.0", "description": "Web Admin Panel for Telegram Bot System", "private": true, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "keywords": ["telegram", "bot", "admin", "panel", "react"], "author": "", "license": "ISC", "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@mui/x-charts": "^8.9.0", "@mui/x-data-grid": "^8.8.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "axios": "^1.10.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.7.0", "react-scripts": "^5.0.1", "recharts": "^3.1.0", "typescript": "^4.9.5"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}