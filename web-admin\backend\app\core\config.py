"""
Configuration settings for Web Admin Panel
"""

import os
from typing import List, Optional, Union
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """Application settings"""
    
    # Application settings
    APP_NAME: str = "Telegram Bot Web Admin Panel"
    VERSION: str = "1.0.0"
    DEBUG: bool = Field(default=False, env="DEBUG")
    
    # Server settings
    HOST: str = Field(default="0.0.0.0", env="HOST")
    PORT: int = Field(default=8000, env="PORT")
    
    # Security settings
    SECRET_KEY: str = Field(..., env="SECRET_KEY")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    REFRESH_TOKEN_EXPIRE_DAYS: int = Field(default=7, env="REFRESH_TOKEN_EXPIRE_DAYS")
    ALGORITHM: str = "HS256"
    
    # CORS settings
    ALLOWED_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "http://127.0.0.1:3000"],
        env="ALLOWED_ORIGINS"
    )
    ALLOWED_HOSTS: List[str] = Field(
        default=["localhost", "127.0.0.1"],
        env="ALLOWED_HOSTS"
    )
    
    # Database settings (MongoDB)
    MONGODB_URI: str = Field(..., env="MONGODB_URI")
    DATABASE_NAME: str = Field(default="referral_bot", env="DATABASE_NAME")
    
    # Redis settings
    REDIS_URL: str = Field(default="redis://localhost:6379", env="REDIS_URL")
    REDIS_PASSWORD: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    
    # Admin settings
    ADMIN_IDS: Union[str, List[int]] = Field(default="", env="ADMIN_IDS")
    SUPER_ADMIN_ID: int = Field(..., env="SUPER_ADMIN_ID")
    
    # Bot settings (for integration)
    BOT_TOKEN: str = Field(..., env="BOT_TOKEN")
    BOT_USERNAME: str = Field(..., env="BOT_USERNAME")
    
    # Logging
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FILE: str = Field(default="web_admin.log", env="LOG_FILE")
    
    # Pagination settings
    DEFAULT_PAGE_SIZE: int = Field(default=20, env="DEFAULT_PAGE_SIZE")
    MAX_PAGE_SIZE: int = Field(default=100, env="MAX_PAGE_SIZE")
    
    # Cache settings
    CACHE_TTL: int = Field(default=300, env="CACHE_TTL")  # 5 minutes
    STATS_CACHE_TTL: int = Field(default=60, env="STATS_CACHE_TTL")  # 1 minute
    
    # WebSocket settings
    WEBSOCKET_HEARTBEAT_INTERVAL: int = Field(default=30, env="WEBSOCKET_HEARTBEAT_INTERVAL")
    
    # File upload settings
    MAX_FILE_SIZE: int = Field(default=10 * 1024 * 1024, env="MAX_FILE_SIZE")  # 10MB
    ALLOWED_FILE_TYPES: Union[str, List[str]] = Field(
        default="image/jpeg,image/png,image/gif,application/pdf",
        env="ALLOWED_FILE_TYPES"
    )
    
    # Rate limiting
    RATE_LIMIT_ENABLED: bool = Field(default=True, env="RATE_LIMIT_ENABLED")
    RATE_LIMIT_REQUESTS: int = Field(default=100, env="RATE_LIMIT_REQUESTS")
    RATE_LIMIT_WINDOW: int = Field(default=60, env="RATE_LIMIT_WINDOW")  # seconds
    
    # Referral visualization settings
    MAX_REFERRAL_DEPTH: int = Field(default=1000, env="MAX_REFERRAL_DEPTH")
    REFERRAL_BATCH_SIZE: int = Field(default=100, env="REFERRAL_BATCH_SIZE")
    
    class Config:
        env_file = ".env"
        case_sensitive = True
        
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # Parse ADMIN_IDS from comma-separated string if needed
        if isinstance(self.ADMIN_IDS, str):
            self.ADMIN_IDS = [
                int(admin_id.strip())
                for admin_id in self.ADMIN_IDS.split(",")
                if admin_id.strip().isdigit()
            ]
        
        # Parse ALLOWED_ORIGINS from comma-separated string if needed
        if isinstance(self.ALLOWED_ORIGINS, str):
            self.ALLOWED_ORIGINS = [
                origin.strip()
                for origin in self.ALLOWED_ORIGINS.split(",")
                if origin.strip()
            ]
        
        # Parse ALLOWED_HOSTS from comma-separated string if needed
        if isinstance(self.ALLOWED_HOSTS, str):
            self.ALLOWED_HOSTS = [
                host.strip()
                for host in self.ALLOWED_HOSTS.split(",")
                if host.strip()
            ]

        # Parse ALLOWED_FILE_TYPES from comma-separated string if needed
        if isinstance(self.ALLOWED_FILE_TYPES, str):
            self.ALLOWED_FILE_TYPES = [
                file_type.strip()
                for file_type in self.ALLOWED_FILE_TYPES.split(",")
                if file_type.strip()
            ]


# Global settings instance
settings = Settings()
