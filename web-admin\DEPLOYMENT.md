# Production Deployment Guide

This guide covers deploying the Telegram Bot Web Admin Panel to production environments.

## 🚀 Quick Production Deployment

### Prerequisites

1. **Server Requirements**
   - Ubuntu 20.04+ or CentOS 8+ (recommended)
   - 4GB RAM minimum (8GB+ recommended for 100k+ users)
   - 50GB disk space minimum
   - Docker and Docker Compose installed

2. **External Services**
   - MongoDB Atlas or self-hosted MongoDB
   - Domain name with DNS access
   - SSL certificate (Let's Encrypt recommended)

### Step 1: Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Logout and login again for group changes
```

### Step 2: Application Setup

```bash
# Clone repository
git clone <your-repo-url>
cd web-admin

# Create production environment file
cp .env.prod.example .env.prod

# Edit configuration
nano .env.prod
```

### Step 3: Configure Environment

Edit `.env.prod` with your production values:

```env
# Database
MONGODB_URI=mongodb+srv://user:<EMAIL>/db
DATABASE_NAME=referral_bot

# Security
SECRET_KEY=your-super-secure-secret-key
ADMIN_IDS=123456789,987654321
SUPER_ADMIN_ID=123456789

# Bot
BOT_TOKEN=your_telegram_bot_token
BOT_USERNAME=YourBotUsername

# Domain
FRONTEND_API_URL=https://your-domain.com/api/v1
FRONTEND_WS_URL=wss://your-domain.com/api/v1/ws
DOMAIN_NAME=your-domain.com
```

### Step 4: SSL Certificate Setup

```bash
# Install Certbot
sudo apt install certbot

# Get SSL certificate
sudo certbot certonly --standalone -d your-domain.com

# Copy certificates
sudo mkdir -p nginx/ssl
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem nginx/ssl/cert.pem
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem nginx/ssl/key.pem
sudo chown -R $USER:$USER nginx/ssl
```

### Step 5: Deploy

```bash
# Make deploy script executable
chmod +x scripts/deploy.sh

# Run deployment
./scripts/deploy.sh deploy
```

## 🔧 Advanced Configuration

### Load Balancing

For high-traffic deployments, configure multiple backend instances:

```yaml
# In docker-compose.prod.yml
services:
  backend:
    deploy:
      replicas: 3
    environment:
      - WORKER_PROCESSES=2
```

### Database Optimization

```javascript
// MongoDB indexes for performance
db.users.createIndex({ "user_id": 1 }, { unique: true })
db.users.createIndex({ "referred_by": 1 })
db.users.createIndex({ "created_at": -1 })
db.withdrawals.createIndex({ "user_id": 1 })
db.withdrawals.createIndex({ "status": 1 })
db.withdrawals.createIndex({ "created_at": -1 })
```

### Redis Configuration

```conf
# redis/redis.conf
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### Monitoring Setup

Enable monitoring services:

```bash
# Start with monitoring
docker-compose -f docker-compose.prod.yml up -d prometheus grafana

# Access Grafana
# URL: http://your-domain:3001
# User: admin
# Password: (from GRAFANA_PASSWORD in .env.prod)
```

## 📊 Performance Tuning

### Backend Optimization

```python
# In backend configuration
WORKER_PROCESSES = 4  # CPU cores
MAX_CONNECTIONS = 1000
KEEPALIVE_TIMEOUT = 65
```

### Frontend Optimization

```nginx
# Nginx caching
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### Database Connection Pooling

```python
# MongoDB connection pool
MONGODB_POOL_SIZE = 10
MONGODB_MAX_OVERFLOW = 20
```

## 🔒 Security Hardening

### Firewall Configuration

```bash
# UFW firewall setup
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

### SSL/TLS Configuration

```nginx
# Strong SSL configuration
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256;
ssl_prefer_server_ciphers off;
add_header Strict-Transport-Security "max-age=63072000" always;
```

### Rate Limiting

```nginx
# API rate limiting
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;
```

## 📋 Maintenance

### Backup Strategy

```bash
# Automated backup script
./scripts/backup.sh

# Manual database backup
mongodump --uri="$MONGODB_URI" --out="./backups/$(date +%Y%m%d)"
```

### Log Management

```bash
# View logs
./scripts/deploy.sh logs

# Specific service logs
docker-compose -f docker-compose.prod.yml logs -f backend

# Log rotation
sudo logrotate -f /etc/logrotate.d/docker-containers
```

### Updates

```bash
# Update application
git pull origin main
./scripts/deploy.sh deploy

# Rollback if needed
./scripts/deploy.sh rollback
```

## 🚨 Troubleshooting

### Common Issues

1. **Service Won't Start**
   ```bash
   # Check logs
   ./scripts/deploy.sh logs backend
   
   # Check health
   ./scripts/deploy.sh health
   ```

2. **Database Connection Issues**
   ```bash
   # Test MongoDB connection
   docker-compose exec backend python -c "from app.core.database import database_manager; print(database_manager.health_check())"
   ```

3. **SSL Certificate Issues**
   ```bash
   # Renew certificate
   sudo certbot renew
   
   # Update nginx
   docker-compose restart nginx
   ```

### Performance Issues

1. **High Memory Usage**
   ```bash
   # Monitor resources
   docker stats
   
   # Adjust worker processes
   # Edit .env.prod: WORKER_PROCESSES=2
   ```

2. **Slow Database Queries**
   ```bash
   # Enable MongoDB profiling
   db.setProfilingLevel(2)
   db.system.profile.find().sort({ts:-1}).limit(5)
   ```

## 📈 Scaling

### Horizontal Scaling

```yaml
# docker-compose.scale.yml
services:
  backend:
    deploy:
      replicas: 5
  
  nginx:
    deploy:
      replicas: 2
```

### Database Scaling

```javascript
// MongoDB sharding setup
sh.enableSharding("referral_bot")
sh.shardCollection("referral_bot.users", {"user_id": 1})
```

### CDN Integration

```nginx
# CloudFlare or AWS CloudFront
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    add_header Cache-Control "public, max-age=31536000";
    expires 1y;
}
```

## 🔍 Monitoring

### Health Checks

```bash
# Automated health monitoring
curl -f http://your-domain.com/health
curl -f http://your-domain.com/api/v1/health
```

### Metrics Collection

```yaml
# Prometheus configuration
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'telegram-bot-admin'
    static_configs:
      - targets: ['backend:8000']
```

### Alerting

```yaml
# Grafana alerts
- alert: HighErrorRate
  expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
  for: 5m
  annotations:
    summary: High error rate detected
```

## 📞 Support

For deployment issues:

1. Check logs: `./scripts/deploy.sh logs`
2. Verify configuration: `./scripts/deploy.sh status`
3. Run health checks: `./scripts/deploy.sh health`
4. Review this guide and documentation

## 🔄 CI/CD Integration

### GitHub Actions

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Deploy to server
        run: |
          ssh user@server 'cd /path/to/app && git pull && ./scripts/deploy.sh deploy'
```

### Automated Testing

```bash
# Pre-deployment tests
npm test                    # Frontend tests
pytest                     # Backend tests
docker-compose -f docker-compose.test.yml up --abort-on-container-exit
```
