import React, { createContext, useContext, ReactNode } from 'react';
import { dashboardService } from '../services/dashboardService';
import { userService } from '../services/userService';
import { withdrawalService } from '../services/withdrawalService';

interface ApiContextType {
  dashboard: typeof dashboardService;
  users: typeof userService;
  withdrawals: typeof withdrawalService;
}

const ApiContext = createContext<ApiContextType | undefined>(undefined);

interface ApiProviderProps {
  children: ReactNode;
}

export function ApiProvider({ children }: ApiProviderProps) {
  const value: ApiContextType = {
    dashboard: dashboardService,
    users: userService,
    withdrawals: withdrawalService,
  };

  return (
    <ApiContext.Provider value={value}>
      {children}
    </ApiContext.Provider>
  );
}

export function useApi(): ApiContextType {
  const context = useContext(ApiContext);
  if (context === undefined) {
    throw new Error('useApi must be used within an ApiProvider');
  }
  return context;
}
