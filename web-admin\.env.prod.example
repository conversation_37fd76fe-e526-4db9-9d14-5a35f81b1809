# Production Environment Configuration for Telegram Bot Admin Panel

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
MONGODB_URI=mongodb+srv://username:<EMAIL>/database_name?retryWrites=true&w=majority
DATABASE_NAME=referral_bot

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=your_secure_redis_password

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
SECRET_KEY=your_super_secure_secret_key_change_this_in_production
ADMIN_IDS=123456789,987654321,555666777
SUPER_ADMIN_ID=123456789

# =============================================================================
# BOT CONFIGURATION
# =============================================================================
BOT_TOKEN=your_telegram_bot_token_here
BOT_USERNAME=YourBotUsername

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================
FRONTEND_API_URL=https://your-domain.com/api/v1
FRONTEND_WS_URL=wss://your-domain.com/api/v1/ws

# =============================================================================
# SSL/TLS CONFIGURATION (for HTTPS)
# =============================================================================
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem
DOMAIN_NAME=your-domain.com

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================
GRAFANA_PASSWORD=secure_grafana_admin_password
PROMETHEUS_RETENTION=30d

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key

# =============================================================================
# EMAIL CONFIGURATION (for alerts)
# =============================================================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
ALERT_EMAIL=<EMAIL>

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================
WORKER_PROCESSES=4
MAX_CONNECTIONS=1024
RATE_LIMIT_API=10
RATE_LIMIT_LOGIN=1

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=INFO
LOG_RETENTION_DAYS=30
SENTRY_DSN=your_sentry_dsn_for_error_tracking

# =============================================================================
# FEATURE FLAGS
# =============================================================================
ENABLE_MONITORING=true
ENABLE_BACKUP=true
ENABLE_SSL=false
ENABLE_RATE_LIMITING=true

# =============================================================================
# SCALING CONFIGURATION
# =============================================================================
BACKEND_REPLICAS=2
FRONTEND_REPLICAS=1
REDIS_REPLICAS=1

# =============================================================================
# MAINTENANCE CONFIGURATION
# =============================================================================
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE=System is under maintenance. Please try again later.

# =============================================================================
# WEBHOOK CONFIGURATION (for external integrations)
# =============================================================================
WEBHOOK_SECRET=your_webhook_secret
WEBHOOK_URL=https://your-domain.com/webhooks

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================
CACHE_TTL_STATS=60
CACHE_TTL_USER_DATA=300
CACHE_TTL_WITHDRAWAL_DATA=180

# =============================================================================
# SECURITY HEADERS
# =============================================================================
CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com
TRUSTED_HOSTS=your-domain.com,www.your-domain.com

# =============================================================================
# DATABASE OPTIMIZATION
# =============================================================================
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30

# =============================================================================
# API RATE LIMITING
# =============================================================================
API_RATE_LIMIT_PER_MINUTE=60
LOGIN_RATE_LIMIT_PER_MINUTE=5
WEBSOCKET_RATE_LIMIT_PER_MINUTE=100

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
MAX_UPLOAD_SIZE=10MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx
UPLOAD_PATH=/app/uploads

# =============================================================================
# SESSION CONFIGURATION
# =============================================================================
SESSION_TIMEOUT=3600
REFRESH_TOKEN_EXPIRE_DAYS=30
ACCESS_TOKEN_EXPIRE_MINUTES=60

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_SLACK_NOTIFICATIONS=false
SLACK_WEBHOOK_URL=your_slack_webhook_url

# =============================================================================
# ANALYTICS CONFIGURATION
# =============================================================================
GOOGLE_ANALYTICS_ID=your_ga_tracking_id
ENABLE_USER_TRACKING=false

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================
PAYMENT_GATEWAY_API_KEY=your_payment_gateway_key
SMS_SERVICE_API_KEY=your_sms_service_key
CAPTCHA_SECRET_KEY=your_captcha_secret

# =============================================================================
# DEVELOPMENT/DEBUG (should be false in production)
# =============================================================================
DEBUG=false
ENABLE_DEBUG_TOOLBAR=false
ENABLE_PROFILING=false
