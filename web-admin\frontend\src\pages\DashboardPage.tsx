import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Al<PERSON>,
  Chip,
} from '@mui/material';
import {
  People as PeopleIcon,
  AccountBalanceWallet as WalletIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import { useApi } from '../contexts/ApiContext';
import { DashboardStats } from '../types';

interface StatCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: React.ComponentType;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

function StatCard({ title, value, subtitle, icon: Icon, color = 'primary', trend }: StatCardProps) {
  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Icon sx={{ fontSize: 40, color: `${color}.main` }} />
          {trend && (
            <Chip
              label={`${trend.isPositive ? '+' : ''}${trend.value}%`}
              size="small"
              color={trend.isPositive ? 'success' : 'error'}
              variant="outlined"
            />
          )}
        </Box>
        
        <Typography variant="h4" sx={{ fontWeight: 600, mb: 1 }}>
          {typeof value === 'number' ? value.toLocaleString() : value}
        </Typography>
        
        <Typography variant="h6" color="text.secondary" sx={{ mb: 0.5 }}>
          {title}
        </Typography>
        
        {subtitle && (
          <Typography variant="body2" color="text.secondary">
            {subtitle}
          </Typography>
        )}
      </CardContent>
    </Card>
  );
}

function DashboardPage() {
  const { dashboard } = useApi();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        const data = await dashboard.getStats();
        setStats(data);
        setError(null);
      } catch (err: any) {
        setError(err.response?.data?.detail || err.message || 'Failed to load dashboard stats');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();

    // Refresh stats every 30 seconds
    const interval = setInterval(fetchStats, 30000);
    return () => clearInterval(interval);
  }, [dashboard]);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        {error}
      </Alert>
    );
  }

  if (!stats) {
    return (
      <Alert severity="warning" sx={{ mb: 3 }}>
        No dashboard data available
      </Alert>
    );
  }

  return (
    <Box>
      {/* Page Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 600, mb: 1 }}>
          Dashboard
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Overview of your telegram bot system
        </Typography>
      </Box>

      {/* Stats Grid */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* User Stats */}
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Users"
            value={stats.user_stats.total_users}
            subtitle={`${stats.user_stats.new_users_today} new today`}
            icon={PeopleIcon}
            color="primary"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Active Users"
            value={stats.user_stats.active_users}
            subtitle={`${stats.user_stats.banned_users} banned`}
            icon={PeopleIcon}
            color="success"
          />
        </Grid>

        {/* Withdrawal Stats */}
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Withdrawals"
            value={stats.withdrawal_stats.total_withdrawals}
            subtitle={`₹${stats.withdrawal_stats.total_amount.toLocaleString()}`}
            icon={WalletIcon}
            color="secondary"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Pending Withdrawals"
            value={stats.withdrawal_stats.pending_withdrawals}
            subtitle={`₹${stats.withdrawal_stats.pending_amount.toLocaleString()}`}
            icon={WarningIcon}
            color="warning"
          />
        </Grid>

        {/* Additional Stats */}
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Balance"
            value={`₹${stats.user_stats.total_balance.toLocaleString()}`}
            subtitle="User balances"
            icon={TrendingUpIcon}
            color="success"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="This Week"
            value={stats.user_stats.new_users_this_week}
            subtitle="New users"
            icon={PeopleIcon}
            color="primary"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="This Month"
            value={stats.user_stats.new_users_this_month}
            subtitle="New users"
            icon={PeopleIcon}
            color="primary"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Today's Withdrawals"
            value={stats.withdrawal_stats.today_withdrawals}
            subtitle={`₹${stats.withdrawal_stats.today_amount.toLocaleString()}`}
            icon={WalletIcon}
            color="secondary"
          />
        </Grid>
      </Grid>

      {/* Quick Actions */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                Top Users by Balance
              </Typography>
              {stats.top_users.by_balance.slice(0, 5).map((user, index) => (
                <Box
                  key={user.user_id}
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    py: 1,
                    borderBottom: index < 4 ? '1px solid' : 'none',
                    borderBottomColor: 'divider',
                  }}
                >
                  <Box>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {user.first_name} {user.last_name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      ID: {user.user_id}
                    </Typography>
                  </Box>
                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                    ₹{user.balance.toLocaleString()}
                  </Typography>
                </Box>
              ))}
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                Withdrawal Methods
              </Typography>
              {stats.withdrawal_method_stats.map((method, index) => (
                <Box
                  key={method.method}
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    py: 1,
                    borderBottom: index < stats.withdrawal_method_stats.length - 1 ? '1px solid' : 'none',
                    borderBottomColor: 'divider',
                  }}
                >
                  <Box>
                    <Typography variant="body2" sx={{ fontWeight: 500, textTransform: 'capitalize' }}>
                      {method.method}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {method.count} transactions
                    </Typography>
                  </Box>
                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                    ₹{method.total_amount.toLocaleString()}
                  </Typography>
                </Box>
              ))}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}

export default DashboardPage;
