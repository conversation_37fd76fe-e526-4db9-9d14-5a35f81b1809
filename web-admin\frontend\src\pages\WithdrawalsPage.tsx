import React from 'react';
import { Box, Typography, Card, CardContent } from '@mui/material';

function WithdrawalsPage() {
  return (
    <Box>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 600, mb: 1 }}>
          Withdrawal Management
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Review, approve, and manage withdrawal requests
        </Typography>
      </Box>

      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Withdrawal Management Features
          </Typography>
          <Typography variant="body2" color="text.secondary">
            This page will include:
            <br />• Withdrawal request queue
            <br />• Approve/reject functionality
            <br />• Withdrawal history and tracking
            <br />• Method-wise statistics
            <br />• Bulk operations
            <br />• Transaction audit trails
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
}

export default WithdrawalsPage;
