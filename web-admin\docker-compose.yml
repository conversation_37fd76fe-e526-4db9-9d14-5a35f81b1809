version: '3.8'

services:
  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: telegram-bot-admin-backend
    ports:
      - "8000:8000"
    environment:
      - DEBUG=false
      - HOST=0.0.0.0
      - PORT=8000
      - MONGODB_URI=${MONGODB_URI}
      - DATABASE_NAME=${DATABASE_NAME}
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY=${SECRET_KEY}
      - ADMIN_IDS=${ADMIN_IDS}
      - SUPER_ADMIN_ID=${SUPER_ADMIN_ID}
      - BOT_TOKEN=${BOT_TOKEN}
      - BOT_USERNAME=${BOT_USERNAME}
    depends_on:
      - redis
    volumes:
      - ./backend/logs:/app/logs
    restart: unless-stopped
    networks:
      - telegram-bot-network

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: telegram-bot-admin-frontend
    ports:
      - "3000:80"
    environment:
      - REACT_APP_API_URL=http://localhost:8000/api/v1
      - REACT_APP_WS_URL=ws://localhost:8000/api/v1/ws
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - telegram-bot-network

  # Redis for caching
  redis:
    image: redis:7-alpine
    container_name: telegram-bot-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - telegram-bot-network
    command: redis-server --appendonly yes

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: telegram-bot-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    restart: unless-stopped
    networks:
      - telegram-bot-network

volumes:
  redis_data:

networks:
  telegram-bot-network:
    driver: bridge
