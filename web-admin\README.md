# Telegram Bot Web Admin Panel

A comprehensive, modern web admin panel for managing Telegram referral bot systems. Built with React (frontend) and FastAPI (backend) with real-time updates via WebSocket connections.

## Features

### 🎯 Core Features
- **Real-time Dashboard** - Live statistics and system monitoring
- **User Management** - Search, filter, ban/unban, balance operations
- **Withdrawal Management** - Approve/reject withdrawals, bulk operations
- **Referral Visualization** - Interactive tree view for deep referral chains (1000+ levels)
- **Admin Management** - Role-based access control and permissions
- **Real-time Updates** - WebSocket connections for live data

### 📊 Analytics & Reporting
- User registration trends
- Withdrawal statistics and patterns
- Referral performance metrics
- Revenue analytics
- System health monitoring

### 🔧 Technical Features
- **Scalable Architecture** - Optimized for 100,000+ users
- **Performance Optimized** - Redis caching, database indexing
- **Responsive Design** - Works on desktop and mobile
- **Security** - JWT authentication, role-based permissions
- **Real-time** - WebSocket connections for live updates

## Technology Stack

### Backend
- **FastAPI** - Modern Python web framework
- **MongoDB** - Document database with existing bot data
- **Redis** - Caching and real-time features
- **WebSocket** - Real-time communication
- **JWT** - Secure authentication

### Frontend
- **React 18** - Modern UI library
- **TypeScript** - Type-safe development
- **Material-UI** - Professional UI components
- **React Router** - Client-side routing
- **Axios** - HTTP client with interceptors

## Quick Start

### Prerequisites
- Python 3.8+
- Node.js 16+
- MongoDB instance
- Redis instance

### Backend Setup

1. **Navigate to backend directory**
   ```bash
   cd web-admin/backend
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

5. **Start the server**
   ```bash
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

### Frontend Setup

1. **Navigate to frontend directory**
   ```bash
   cd web-admin/frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your API URL
   ```

4. **Start development server**
   ```bash
   npm start
   ```

The application will be available at:
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- API Documentation: http://localhost:8000/docs

## Configuration

### Backend Configuration (.env)

```env
# Database
MONGODB_URI=mongodb+srv://username:<EMAIL>/database_name
DATABASE_NAME=referral_bot

# Redis
REDIS_URL=redis://localhost:6379

# Security
SECRET_KEY=your-super-secret-key-change-this
ADMIN_IDS=123456789,987654321
SUPER_ADMIN_ID=123456789

# Bot Integration
BOT_TOKEN=your_bot_token_here
BOT_USERNAME=YourBotUsername
```

### Frontend Configuration (.env)

```env
REACT_APP_API_URL=http://localhost:8000/api/v1
REACT_APP_WS_URL=ws://localhost:8000/api/v1/ws
```

## Database Integration

The admin panel integrates with your existing MongoDB database structure:

### Required Collections
- `users` - User data and profiles
- `withdrawals` - Withdrawal requests and history
- `admin_settings` - Admin configurations
- `tasks` - Task management
- `gift_codes` - Gift code system
- `broadcast_logs` - Message broadcasting

### Automatic Indexing
The system automatically creates optimized indexes for:
- User searches and filtering
- Withdrawal queries
- Referral lookups
- Performance optimization

## API Endpoints

### Authentication
- `POST /api/v1/auth/login` - Admin login
- `POST /api/v1/auth/logout` - Admin logout
- `GET /api/v1/auth/me` - Get current admin info

### Dashboard
- `GET /api/v1/dashboard/stats` - Dashboard statistics
- `GET /api/v1/dashboard/recent-activity` - Recent activity
- `GET /api/v1/dashboard/system-health` - System health check

### User Management
- `GET /api/v1/users/` - Search and list users
- `GET /api/v1/users/{user_id}` - Get user details
- `POST /api/v1/users/{user_id}/ban` - Ban user
- `POST /api/v1/users/{user_id}/balance` - Update balance

### Withdrawal Management
- `GET /api/v1/withdrawals/` - Search withdrawals
- `POST /api/v1/withdrawals/{id}/approve` - Approve withdrawal
- `POST /api/v1/withdrawals/bulk-action` - Bulk operations

## Security

### Authentication
- JWT-based authentication
- Token refresh mechanism
- Secure password hashing

### Authorization
- Role-based access control
- Permission-based route protection
- Admin ID validation

### Security Headers
- CORS configuration
- Trusted host middleware
- Request rate limiting

## Performance Optimization

### Caching Strategy
- Redis caching for frequently accessed data
- Statistics caching (1-minute TTL)
- User session management

### Database Optimization
- Automatic indexing for common queries
- Aggregation pipelines for statistics
- Connection pooling

### Frontend Optimization
- Code splitting and lazy loading
- Optimized bundle size
- Efficient re-rendering

## Deployment

### Production Backend
```bash
# Using Gunicorn
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000

# Using Docker
docker build -t telegram-bot-admin-backend .
docker run -p 8000:8000 telegram-bot-admin-backend
```

### Production Frontend
```bash
# Build for production
npm run build

# Serve with nginx or any static file server
```

### Environment Variables
Set production environment variables:
- `DEBUG=false`
- `SECRET_KEY=production-secret-key`
- `MONGODB_URI=production-mongodb-uri`
- `REDIS_URL=production-redis-url`

## Monitoring

### Health Checks
- Database connectivity
- Redis connectivity
- WebSocket status
- System resources

### Logging
- Structured logging with timestamps
- Error tracking and reporting
- Admin action audit logs

## Support

### Common Issues
1. **Database Connection** - Check MongoDB URI and network access
2. **Redis Connection** - Verify Redis server is running
3. **WebSocket Issues** - Check firewall and proxy settings
4. **Permission Errors** - Verify admin IDs in configuration

### Development
- API documentation available at `/docs`
- WebSocket testing tools included
- Comprehensive error handling

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Changelog

### v1.0.0
- Initial release
- Complete admin panel functionality
- Real-time dashboard
- User and withdrawal management
- Referral system visualization
- WebSocket integration
