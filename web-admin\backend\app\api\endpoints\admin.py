"""
Admin management endpoints for Web Admin Panel
"""

import logging
from typing import List, Optional
from fastapi import APIRouter, HTTPException, status, Depends, Query

from app.core.auth import require_super_admin, require_permission, auth_service
from app.core.database import database_manager
from app.models.auth import AdminCreate, AdminUpdate, AdminPermissions
from app.services.realtime_service import realtime_service

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/list")
async def list_admins(
    current_admin: dict = Depends(require_super_admin())
):
    """List all admins"""
    try:
        admins = await database_manager.admin_settings.find({}).to_list(length=None)

        # Remove sensitive information
        for admin in admins:
            admin.pop("password", None)

        return {"admins": admins}

    except Exception as e:
        logger.error(f"Error listing admins: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving admin list"
        )


@router.post("/create")
async def create_admin(
    admin_data: AdminCreate,
    current_admin: dict = Depends(require_super_admin())
):
    """Create new admin"""
    try:
        # Check if admin already exists
        existing_admin = await database_manager.admin_settings.find_one(
            {"admin_id": admin_data.admin_id}
        )

        if existing_admin:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Admin already exists"
            )

        # Create admin document
        admin_doc = {
            "admin_id": admin_data.admin_id,
            "role": admin_data.role,
            "permissions": admin_data.permissions.dict(),
            "created_at": database_manager.get_current_timestamp(),
            "updated_at": database_manager.get_current_timestamp(),
            "created_by": current_admin["admin_id"],
            "is_active": True,
            "last_login": None
        }

        # Set password if provided
        if admin_data.password:
            admin_doc["password"] = auth_service.get_password_hash(admin_data.password)

        # Insert admin
        await database_manager.admin_settings.insert_one(admin_doc)

        # Log action
        await database_manager.admin_logs.insert_one({
            "admin_id": current_admin["admin_id"],
            "action": "create_admin",
            "metadata": {"new_admin_id": admin_data.admin_id},
            "timestamp": database_manager.get_current_timestamp()
        })

        return {"message": f"Admin {admin_data.admin_id} created successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating admin: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error creating admin"
        )


@router.put("/{admin_id}")
async def update_admin(
    admin_id: int,
    admin_update: AdminUpdate,
    current_admin: dict = Depends(require_super_admin())
):
    """Update admin information"""
    try:
        # Check if admin exists
        existing_admin = await database_manager.admin_settings.find_one(
            {"admin_id": admin_id}
        )

        if not existing_admin:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Admin not found"
            )

        # Prepare update data
        update_data = admin_update.dict(exclude_unset=True)
        if update_data:
            update_data["updated_at"] = database_manager.get_current_timestamp()

            # Convert permissions to dict if provided
            if "permissions" in update_data and update_data["permissions"]:
                update_data["permissions"] = update_data["permissions"].dict()

            # Update admin
            result = await database_manager.admin_settings.update_one(
                {"admin_id": admin_id},
                {"$set": update_data}
            )

            if result.modified_count > 0:
                # Log action
                await database_manager.admin_logs.insert_one({
                    "admin_id": current_admin["admin_id"],
                    "action": "update_admin",
                    "metadata": {"target_admin_id": admin_id, "updates": update_data},
                    "timestamp": database_manager.get_current_timestamp()
                })

                return {"message": f"Admin {admin_id} updated successfully"}
            else:
                return {"message": "No changes made"}

        return {"message": "No update data provided"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating admin {admin_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error updating admin"
        )


@router.delete("/{admin_id}")
async def delete_admin(
    admin_id: int,
    current_admin: dict = Depends(require_super_admin())
):
    """Delete/deactivate admin"""
    try:
        # Prevent self-deletion
        if admin_id == current_admin["admin_id"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete your own admin account"
            )

        # Check if admin exists
        existing_admin = await database_manager.admin_settings.find_one(
            {"admin_id": admin_id}
        )

        if not existing_admin:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Admin not found"
            )

        # Deactivate instead of delete
        result = await database_manager.admin_settings.update_one(
            {"admin_id": admin_id},
            {
                "$set": {
                    "is_active": False,
                    "updated_at": database_manager.get_current_timestamp(),
                    "deactivated_by": current_admin["admin_id"]
                }
            }
        )

        if result.modified_count > 0:
            # Log action
            await database_manager.admin_logs.insert_one({
                "admin_id": current_admin["admin_id"],
                "action": "deactivate_admin",
                "metadata": {"target_admin_id": admin_id},
                "timestamp": database_manager.get_current_timestamp()
            })

            return {"message": f"Admin {admin_id} deactivated successfully"}

        return {"message": "No changes made"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting admin {admin_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error deleting admin"
        )


@router.get("/bot-settings")
async def get_bot_settings(
    current_admin: dict = Depends(require_permission("manage_bot_settings"))
):
    """Get bot configuration settings"""
    try:
        # Get bot settings from database
        bot_settings = await database_manager.bot_info.find_one({}) or {}

        return {
            "maintenance_mode": bot_settings.get("maintenance_mode", False),
            "bank_withdrawals_enabled": bot_settings.get("bank_withdrawals_enabled", True),
            "usdt_withdrawals_enabled": bot_settings.get("usdt_withdrawals_enabled", True),
            "level_rewards_enabled": bot_settings.get("level_rewards_enabled", True),
            "referral_system_enabled": bot_settings.get("referral_system_enabled", True),
            "gift_codes_enabled": bot_settings.get("gift_codes_enabled", True),
            "force_subscribe_enabled": bot_settings.get("force_subscribe_enabled", True),
            "custom_referral_links_enabled": bot_settings.get("custom_referral_links_enabled", True)
        }

    except Exception as e:
        logger.error(f"Error getting bot settings: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving bot settings"
        )


@router.post("/bot-settings")
async def update_bot_settings(
    settings: dict,
    current_admin: dict = Depends(require_permission("manage_bot_settings"))
):
    """Update bot configuration settings"""
    try:
        # Update bot settings
        await database_manager.bot_info.update_one(
            {},
            {
                "$set": {
                    **settings,
                    "updated_at": database_manager.get_current_timestamp(),
                    "updated_by": current_admin["admin_id"]
                }
            },
            upsert=True
        )

        # Log action
        await database_manager.admin_logs.insert_one({
            "admin_id": current_admin["admin_id"],
            "action": "update_bot_settings",
            "metadata": {"settings": settings},
            "timestamp": database_manager.get_current_timestamp()
        })

        return {"message": "Bot settings updated successfully"}

    except Exception as e:
        logger.error(f"Error updating bot settings: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error updating bot settings"
        )
