import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Toolbar,
  Typography,
  Divider,
  Chip,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  AccountBalanceWallet as WalletIcon,
  Share as ShareIcon,
  AdminPanelSettings as AdminIcon,
  Settings as SettingsIcon,
  TrendingUp as TrendingUpIcon,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useWebSocket } from '../../contexts/WebSocketContext';

interface NavigationItem {
  title: string;
  path: string;
  icon: React.ComponentType;
  permission?: string;
  badge?: number;
}

const navigationItems: NavigationItem[] = [
  {
    title: 'Dashboard',
    path: '/dashboard',
    icon: DashboardIcon,
    permission: 'view_dashboard',
  },
  {
    title: 'User Management',
    path: '/users',
    icon: PeopleIcon,
    permission: 'manage_users',
  },
  {
    title: 'Withdrawals',
    path: '/withdrawals',
    icon: WalletIcon,
    permission: 'manage_withdrawals',
  },
  {
    title: 'Referral System',
    path: '/referrals',
    icon: ShareIcon,
    permission: 'manage_referrals',
  },
  {
    title: 'Admin Management',
    path: '/admin',
    icon: AdminIcon,
    permission: 'manage_admins',
  },
  {
    title: 'Settings',
    path: '/settings',
    icon: SettingsIcon,
  },
];

interface SidebarProps {
  onItemClick?: () => void;
}

function Sidebar({ onItemClick }: SidebarProps) {
  const location = useLocation();
  const navigate = useNavigate();
  const { hasPermission, admin } = useAuth();
  const { isConnected } = useWebSocket();

  const handleItemClick = (path: string) => {
    navigate(path);
    onItemClick?.();
  };

  const filteredItems = navigationItems.filter(item => 
    !item.permission || hasPermission(item.permission)
  );

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Logo/Brand area */}
      <Toolbar>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <TrendingUpIcon color="primary" sx={{ fontSize: 32 }} />
          <Typography variant="h6" sx={{ fontWeight: 600, color: 'primary.main' }}>
            Bot Admin
          </Typography>
        </Box>
      </Toolbar>

      <Divider />

      {/* Admin info */}
      <Box sx={{ p: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
          <Typography variant="subtitle2" color="text.secondary">
            Logged in as:
          </Typography>
          <Chip
            label={isConnected ? 'Online' : 'Offline'}
            size="small"
            color={isConnected ? 'success' : 'default'}
            variant="outlined"
          />
        </Box>
        <Typography variant="body2" sx={{ fontWeight: 500 }}>
          Admin ID: {admin?.admin_id}
        </Typography>
        <Typography variant="caption" color="text.secondary">
          Role: {admin?.role || 'Admin'}
        </Typography>
      </Box>

      <Divider />

      {/* Navigation */}
      <List sx={{ flexGrow: 1, pt: 1 }}>
        {filteredItems.map((item) => {
          const Icon = item.icon;
          const isActive = location.pathname === item.path;

          return (
            <ListItem key={item.path} disablePadding sx={{ px: 1 }}>
              <ListItemButton
                onClick={() => handleItemClick(item.path)}
                selected={isActive}
                sx={{
                  borderRadius: 2,
                  mx: 1,
                  mb: 0.5,
                  '&.Mui-selected': {
                    backgroundColor: 'primary.main',
                    color: 'primary.contrastText',
                    '&:hover': {
                      backgroundColor: 'primary.dark',
                    },
                    '& .MuiListItemIcon-root': {
                      color: 'primary.contrastText',
                    },
                  },
                  '&:hover': {
                    backgroundColor: isActive ? 'primary.dark' : 'action.hover',
                  },
                }}
              >
                <ListItemIcon
                  sx={{
                    color: isActive ? 'inherit' : 'text.secondary',
                    minWidth: 40,
                  }}
                >
                  <Icon />
                </ListItemIcon>
                <ListItemText
                  primary={item.title}
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive ? 600 : 500,
                  }}
                />
                {item.badge && (
                  <Chip
                    label={item.badge}
                    size="small"
                    color="error"
                    sx={{ ml: 1, minWidth: 20, height: 20 }}
                  />
                )}
              </ListItemButton>
            </ListItem>
          );
        })}
      </List>

      {/* Footer */}
      <Box sx={{ p: 2, borderTop: '1px solid', borderTopColor: 'divider' }}>
        <Typography variant="caption" color="text.secondary" align="center" display="block">
          Telegram Bot Admin Panel
        </Typography>
        <Typography variant="caption" color="text.secondary" align="center" display="block">
          v1.0.0
        </Typography>
      </Box>
    </Box>
  );
}

export default Sidebar;
