import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>,
  TextField,
  Button,
  CircularProgress,
  Alert,
  Chip,
  IconButton,
  Tooltip,
  <PERSON>lapse,
  Grid,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Person as PersonIcon,
  AccountTree as TreeIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { useApi } from '../../contexts/ApiContext';

interface ReferralNode {
  user_id: number;
  first_name: string;
  username: string;
  balance: number;
  total_referrals: number;
  direct_referrals: number;
  level: number;
  referred_by: string;
  created_at: number;
  children: ReferralNode[];
}

interface ReferralTree {
  root_user: ReferralNode;
  total_nodes: number;
  max_depth: number;
  total_earnings: number;
}

interface ReferralNodeComponentProps {
  node: ReferralNode;
  isExpanded: boolean;
  onToggle: (userId: number) => void;
  expandedNodes: Set<number>;
  maxDepthToShow: number;
}

function ReferralNodeComponent({
  node,
  isExpanded,
  onToggle,
  expandedNodes,
  maxDepthToShow,
}: ReferralNodeComponentProps) {
  const hasChildren = node.children && node.children.length > 0;
  const shouldShowChildren = isExpanded && node.level < maxDepthToShow;

  const getNodeColor = (level: number) => {
    const colors = [
      '#1976d2', // Blue
      '#388e3c', // Green
      '#f57c00', // Orange
      '#7b1fa2', // Purple
      '#c62828', // Red
      '#00796b', // Teal
    ];
    return colors[level % colors.length];
  };

  return (
    <Box sx={{ ml: node.level * 3, mb: 1 }}>
      <Card
        sx={{
          borderLeft: `4px solid ${getNodeColor(node.level)}`,
          backgroundColor: node.level === 0 ? 'primary.light' : 'background.paper',
          opacity: node.level > maxDepthToShow ? 0.6 : 1,
        }}
      >
        <CardContent sx={{ py: 1.5, '&:last-child': { pb: 1.5 } }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {hasChildren && (
                <IconButton
                  size="small"
                  onClick={() => onToggle(node.user_id)}
                  sx={{ p: 0.5 }}
                >
                  {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
              )}
              
              <PersonIcon sx={{ color: getNodeColor(node.level), fontSize: 20 }} />
              
              <Box>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>
                  {node.first_name} {node.username ? `(@${node.username})` : ''}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  ID: {node.user_id} | Level: {node.level}
                </Typography>
              </Box>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Chip
                label={`₹${node.balance.toLocaleString()}`}
                size="small"
                color="primary"
                variant="outlined"
              />
              <Chip
                label={`${node.direct_referrals} direct`}
                size="small"
                color="secondary"
                variant="outlined"
              />
              {node.total_referrals > node.direct_referrals && (
                <Chip
                  label={`${node.total_referrals} total`}
                  size="small"
                  color="success"
                  variant="outlined"
                />
              )}
            </Box>
          </Box>
        </CardContent>
      </Card>

      <Collapse in={shouldShowChildren}>
        <Box sx={{ mt: 1 }}>
          {node.children.map((child) => (
            <ReferralNodeComponent
              key={child.user_id}
              node={child}
              isExpanded={expandedNodes.has(child.user_id)}
              onToggle={onToggle}
              expandedNodes={expandedNodes}
              maxDepthToShow={maxDepthToShow}
            />
          ))}
        </Box>
      </Collapse>
    </Box>
  );
}

interface ReferralTreeVisualizationProps {
  userId?: number;
}

function ReferralTreeVisualization({ userId: initialUserId }: ReferralTreeVisualizationProps) {
  const { users } = useApi();
  const [userId, setUserId] = useState<string>(initialUserId?.toString() || '');
  const [tree, setTree] = useState<ReferralTree | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expandedNodes, setExpandedNodes] = useState<Set<number>>(new Set());
  const [maxDepth, setMaxDepth] = useState<number>(5);
  const [maxDepthToShow, setMaxDepthToShow] = useState<number>(3);

  const fetchReferralTree = useCallback(async (targetUserId: number) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/v1/referrals/tree/${targetUserId}?max_depth=${maxDepth}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch referral tree');
      }

      const treeData: ReferralTree = await response.json();
      setTree(treeData);
      
      // Auto-expand root node
      setExpandedNodes(new Set([treeData.root_user.user_id]));
      
    } catch (err: any) {
      setError(err.message || 'Failed to load referral tree');
    } finally {
      setLoading(false);
    }
  }, [maxDepth]);

  const handleSearch = () => {
    const userIdNum = parseInt(userId);
    if (userIdNum && !isNaN(userIdNum)) {
      fetchReferralTree(userIdNum);
    }
  };

  const handleToggleNode = (nodeUserId: number) => {
    setExpandedNodes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(nodeUserId)) {
        newSet.delete(nodeUserId);
      } else {
        newSet.add(nodeUserId);
      }
      return newSet;
    });
  };

  const handleExpandAll = () => {
    if (!tree) return;
    
    const getAllNodeIds = (node: ReferralNode): number[] => {
      const ids = [node.user_id];
      node.children.forEach(child => {
        ids.push(...getAllNodeIds(child));
      });
      return ids;
    };
    
    const allIds = getAllNodeIds(tree.root_user);
    setExpandedNodes(new Set(allIds));
  };

  const handleCollapseAll = () => {
    if (tree) {
      setExpandedNodes(new Set([tree.root_user.user_id]));
    }
  };

  useEffect(() => {
    if (initialUserId) {
      fetchReferralTree(initialUserId);
    }
  }, [initialUserId, fetchReferralTree]);

  return (
    <Box>
      {/* Search Controls */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="User ID"
                value={userId}
                onChange={(e) => setUserId(e.target.value)}
                placeholder="Enter user ID to visualize"
                type="number"
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <TextField
                fullWidth
                label="Max Depth"
                value={maxDepth}
                onChange={(e) => setMaxDepth(parseInt(e.target.value) || 5)}
                type="number"
                inputProps={{ min: 1, max: 20 }}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <TextField
                fullWidth
                label="Show Depth"
                value={maxDepthToShow}
                onChange={(e) => setMaxDepthToShow(parseInt(e.target.value) || 3)}
                type="number"
                inputProps={{ min: 1, max: maxDepth }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="contained"
                  startIcon={<SearchIcon />}
                  onClick={handleSearch}
                  disabled={loading || !userId}
                >
                  Search
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  onClick={() => userId && handleSearch()}
                  disabled={loading || !userId}
                >
                  Refresh
                </Button>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Tree Controls */}
      {tree && (
        <Card sx={{ mb: 2 }}>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Box>
                <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <TreeIcon />
                  Referral Tree for {tree.root_user.first_name}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Nodes: {tree.total_nodes} | Max Depth: {tree.max_depth} | 
                  Showing Depth: {maxDepthToShow}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button size="small" onClick={handleExpandAll}>
                  Expand All
                </Button>
                <Button size="small" onClick={handleCollapseAll}>
                  Collapse All
                </Button>
              </Box>
            </Box>
          </CardContent>
        </Card>
      )}

      {/* Loading State */}
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {/* Error State */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Tree Visualization */}
      {tree && !loading && (
        <Card>
          <CardContent>
            <ReferralNodeComponent
              node={tree.root_user}
              isExpanded={expandedNodes.has(tree.root_user.user_id)}
              onToggle={handleToggleNode}
              expandedNodes={expandedNodes}
              maxDepthToShow={maxDepthToShow}
            />
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {!tree && !loading && !error && (
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 4 }}>
            <TreeIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary">
              Enter a User ID to visualize their referral tree
            </Typography>
          </CardContent>
        </Card>
      )}
    </Box>
  );
}

export default ReferralTreeVisualization;
