"""
User service for Web Admin Panel
Handles all user-related database operations
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from pymongo import ASCENDING, DESCENDING

from app.core.database import database_manager
from app.core.redis_client import redis_client
from app.core.config import settings
from app.models.user import (
    User, UserCreate, UserUpdate, UserStats, 
    UserListResponse, UserSearchRequest, BalanceOperation
)

logger = logging.getLogger(__name__)


class UserService:
    """Service for user management operations"""
    
    def __init__(self):
        self.db = database_manager
        self.cache = redis_client
    
    async def get_user_stats(self) -> UserStats:
        """Get comprehensive user statistics"""
        try:
            # Check cache first
            cache_key = "user_stats"
            cached_stats = await self.cache.get(cache_key)
            if cached_stats:
                return UserStats(**cached_stats)
            
            # Calculate current timestamps
            now = datetime.now()
            today_start = int(now.replace(hour=0, minute=0, second=0, microsecond=0).timestamp())
            week_start = int((now - timedelta(days=7)).timestamp())
            month_start = int((now - timedelta(days=30)).timestamp())
            
            # Aggregate user statistics
            pipeline = [
                {
                    "$facet": {
                        "total_users": [{"$count": "count"}],
                        "active_users": [
                            {"$match": {"banned": False}},
                            {"$count": "count"}
                        ],
                        "banned_users": [
                            {"$match": {"banned": True}},
                            {"$count": "count"}
                        ],
                        "new_users_today": [
                            {"$match": {"created_at": {"$gte": today_start}}},
                            {"$count": "count"}
                        ],
                        "new_users_week": [
                            {"$match": {"created_at": {"$gte": week_start}}},
                            {"$count": "count"}
                        ],
                        "new_users_month": [
                            {"$match": {"created_at": {"$gte": month_start}}},
                            {"$count": "count"}
                        ],
                        "balance_stats": [
                            {
                                "$group": {
                                    "_id": None,
                                    "total_balance": {"$sum": "$balance"},
                                    "total_withdrawals": {"$sum": "$successful_withdraw"},
                                    "pending_withdrawals": {"$sum": "$withdraw_under_review"}
                                }
                            }
                        ]
                    }
                }
            ]
            
            result = await self.db.users.aggregate(pipeline).to_list(1)
            if not result:
                return UserStats(
                    total_users=0, active_users=0, banned_users=0,
                    new_users_today=0, new_users_this_week=0, new_users_this_month=0,
                    total_balance=0, total_withdrawals=0, pending_withdrawals=0
                )
            
            data = result[0]
            
            stats = UserStats(
                total_users=data["total_users"][0]["count"] if data["total_users"] else 0,
                active_users=data["active_users"][0]["count"] if data["active_users"] else 0,
                banned_users=data["banned_users"][0]["count"] if data["banned_users"] else 0,
                new_users_today=data["new_users_today"][0]["count"] if data["new_users_today"] else 0,
                new_users_this_week=data["new_users_week"][0]["count"] if data["new_users_week"] else 0,
                new_users_this_month=data["new_users_month"][0]["count"] if data["new_users_month"] else 0,
                total_balance=data["balance_stats"][0]["total_balance"] if data["balance_stats"] else 0,
                total_withdrawals=data["balance_stats"][0]["total_withdrawals"] if data["balance_stats"] else 0,
                pending_withdrawals=data["balance_stats"][0]["pending_withdrawals"] if data["balance_stats"] else 0
            )
            
            # Cache for 1 minute
            await self.cache.set(cache_key, stats.dict(), ttl=settings.STATS_CACHE_TTL)
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting user stats: {e}")
            raise
    
    async def search_users(self, search_request: UserSearchRequest) -> UserListResponse:
        """Search users with filters and pagination"""
        try:
            # Build query filter
            query_filter = {}
            
            if search_request.query:
                # Search in multiple fields
                if search_request.query.isdigit():
                    # If query is numeric, search by user_id
                    query_filter["user_id"] = int(search_request.query)
                else:
                    # Otherwise search in text fields
                    query_filter["$or"] = [
                        {"first_name": {"$regex": search_request.query, "$options": "i"}},
                        {"last_name": {"$regex": search_request.query, "$options": "i"}},
                        {"username": {"$regex": search_request.query, "$options": "i"}}
                    ]
            
            if search_request.banned is not None:
                query_filter["banned"] = search_request.banned
            
            if search_request.has_balance:
                query_filter["balance"] = {"$gt": 0}
            
            if search_request.has_withdrawals:
                query_filter["$or"] = [
                    {"successful_withdraw": {"$gt": 0}},
                    {"withdraw_under_review": {"$gt": 0}}
                ]
            
            if search_request.referred_by and search_request.referred_by != "None":
                query_filter["referred_by"] = search_request.referred_by
            
            # Calculate pagination
            skip = (search_request.page - 1) * search_request.page_size
            limit = min(search_request.page_size, settings.MAX_PAGE_SIZE)
            
            # Determine sort order
            sort_direction = DESCENDING if search_request.sort_order == "desc" else ASCENDING
            
            # Get total count
            total = await self.db.users.count_documents(query_filter)
            
            # Get users
            cursor = self.db.users.find(query_filter).sort(
                search_request.sort_by, sort_direction
            ).skip(skip).limit(limit)
            
            users_data = await cursor.to_list(length=limit)
            users = [User(**user_data) for user_data in users_data]
            
            total_pages = (total + search_request.page_size - 1) // search_request.page_size
            
            return UserListResponse(
                users=users,
                total=total,
                page=search_request.page,
                page_size=search_request.page_size,
                total_pages=total_pages
            )
            
        except Exception as e:
            logger.error(f"Error searching users: {e}")
            raise
    
    async def get_user_by_id(self, user_id: int) -> Optional[User]:
        """Get user by ID"""
        try:
            user_data = await self.db.users.find_one({"user_id": user_id})
            if user_data:
                return User(**user_data)
            return None
        except Exception as e:
            logger.error(f"Error getting user {user_id}: {e}")
            raise
    
    async def update_user(self, user_id: int, user_update: UserUpdate) -> bool:
        """Update user information"""
        try:
            update_data = user_update.dict(exclude_unset=True)
            if not update_data:
                return True
            
            update_data["updated_at"] = self.db.get_current_timestamp()
            
            result = await self.db.users.update_one(
                {"user_id": user_id},
                {"$set": update_data}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Error updating user {user_id}: {e}")
            raise
    
    async def ban_user(self, user_id: int, admin_id: int) -> bool:
        """Ban a user"""
        try:
            result = await self.db.users.update_one(
                {"user_id": user_id},
                {
                    "$set": {
                        "banned": True,
                        "updated_at": self.db.get_current_timestamp()
                    }
                }
            )
            
            if result.modified_count > 0:
                # Log admin action
                await self._log_admin_action(admin_id, "ban_user", {"user_id": user_id})

                # Broadcast real-time update
                try:
                    from app.services.realtime_service import realtime_service
                    await realtime_service.broadcast_user_update(
                        user_id, "banned", {"admin_id": admin_id}
                    )
                except Exception as e:
                    logger.error(f"Error broadcasting user ban update: {e}")

                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error banning user {user_id}: {e}")
            raise
    
    async def unban_user(self, user_id: int, admin_id: int) -> bool:
        """Unban a user"""
        try:
            result = await self.db.users.update_one(
                {"user_id": user_id},
                {
                    "$set": {
                        "banned": False,
                        "updated_at": self.db.get_current_timestamp()
                    }
                }
            )
            
            if result.modified_count > 0:
                # Log admin action
                await self._log_admin_action(admin_id, "unban_user", {"user_id": user_id})

                # Broadcast real-time update
                try:
                    from app.services.realtime_service import realtime_service
                    await realtime_service.broadcast_user_update(
                        user_id, "unbanned", {"admin_id": admin_id}
                    )
                except Exception as e:
                    logger.error(f"Error broadcasting user unban update: {e}")

                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error unbanning user {user_id}: {e}")
            raise
    
    async def update_user_balance(self, balance_op: BalanceOperation) -> bool:
        """Update user balance (credit or debit)"""
        try:
            user = await self.get_user_by_id(balance_op.user_id)
            if not user:
                return False
            
            if balance_op.operation == "credit":
                new_balance = user.balance + balance_op.amount
            elif balance_op.operation == "debit":
                new_balance = max(0, user.balance - balance_op.amount)
            else:
                raise ValueError("Invalid operation. Must be 'credit' or 'debit'")
            
            result = await self.db.users.update_one(
                {"user_id": balance_op.user_id},
                {
                    "$set": {
                        "balance": new_balance,
                        "updated_at": self.db.get_current_timestamp()
                    }
                }
            )
            
            if result.modified_count > 0:
                # Log admin action
                await self._log_admin_action(
                    balance_op.admin_id,
                    f"balance_{balance_op.operation}",
                    {
                        "user_id": balance_op.user_id,
                        "amount": balance_op.amount,
                        "reason": balance_op.reason,
                        "old_balance": user.balance,
                        "new_balance": new_balance
                    }
                )
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error updating balance for user {balance_op.user_id}: {e}")
            raise
    
    async def get_top_users_by_balance(self, limit: int = 10) -> List[User]:
        """Get top users by balance"""
        try:
            cursor = self.db.users.find({"banned": False}).sort("balance", DESCENDING).limit(limit)
            users_data = await cursor.to_list(length=limit)
            return [User(**user_data) for user_data in users_data]
        except Exception as e:
            logger.error(f"Error getting top users by balance: {e}")
            raise
    
    async def get_top_users_by_referrals(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get top users by referral count"""
        try:
            pipeline = [
                {
                    "$lookup": {
                        "from": "users",
                        "localField": "user_id",
                        "foreignField": "referred_by",
                        "as": "referrals"
                    }
                },
                {
                    "$addFields": {
                        "referral_count": {"$size": "$referrals"}
                    }
                },
                {"$match": {"banned": False, "referral_count": {"$gt": 0}}},
                {"$sort": {"referral_count": -1}},
                {"$limit": limit},
                {
                    "$project": {
                        "user_id": 1,
                        "first_name": 1,
                        "username": 1,
                        "balance": 1,
                        "referral_count": 1
                    }
                }
            ]
            
            result = await self.db.users.aggregate(pipeline).to_list(limit)
            return result
            
        except Exception as e:
            logger.error(f"Error getting top users by referrals: {e}")
            raise
    
    async def _log_admin_action(self, admin_id: int, action: str, metadata: Dict[str, Any]):
        """Log admin action"""
        try:
            log_entry = {
                "admin_id": admin_id,
                "action": action,
                "metadata": metadata,
                "timestamp": self.db.get_current_timestamp()
            }
            
            await self.db.admin_logs.insert_one(log_entry)
            
        except Exception as e:
            logger.error(f"Error logging admin action: {e}")


# Global user service instance
user_service = UserService()
